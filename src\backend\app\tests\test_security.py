"""
Security and permissions tests
"""

import pytest
from typing import cast
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from ..models.user import User, UserRole
from ..core.permissions import PermissionChecker, Permission, RolePermissions
from ..core.security import validate_ioc, sanitize_input, rate_limiter
from ..services.auth_service import AuthService


class TestPermissions:
    """Test permission system"""
    
    def test_role_permissions(self):
        """Test role-based permissions"""
        # Test admin permissions
        admin_perms = PermissionChecker.get_role_permissions(UserRole.ADMIN)
        assert Permission.IOC_READ in admin_perms
        assert Permission.IOC_WRITE in admin_perms
        assert Permission.IOC_DELETE in admin_perms
        assert Permission.SYSTEM_CONFIG_WRITE in admin_perms
        assert Permission.USER_WRITE in admin_perms
        
        # Test analyst permissions
        analyst_perms = PermissionChecker.get_role_permissions(UserRole.ANALYST)
        assert Permission.IOC_READ in analyst_perms
        assert Permission.IOC_WRITE in analyst_perms
        assert Permission.IOC_DELETE not in analyst_perms
        assert Permission.SYSTEM_CONFIG_WRITE not in analyst_perms
        assert Permission.USER_WRITE not in analyst_perms
        
        # Test viewer permissions
        viewer_perms = PermissionChecker.get_role_permissions(UserRole.VIEWER)
        assert Permission.IOC_READ in viewer_perms
        assert Permission.IOC_WRITE not in viewer_perms
        assert Permission.IOC_DELETE not in viewer_perms
        
        # Test API user permissions
        api_perms = PermissionChecker.get_role_permissions(UserRole.API_USER)
        assert Permission.IOC_READ in api_perms
        assert Permission.IOC_WRITE in api_perms
        assert Permission.PASSIVE_SCAN in api_perms
        assert Permission.USER_WRITE not in api_perms
    
    def test_user_permissions(self):
        """Test user permission checking"""
        # Create admin user
        admin_user = User(
            username="admin",
            role="admin",
            permissions=[]
        )
        
        # Test admin permissions
        assert PermissionChecker.has_permission(admin_user, Permission.IOC_READ)
        assert PermissionChecker.has_permission(admin_user, Permission.SYSTEM_CONFIG_WRITE)
        assert PermissionChecker.has_permission(admin_user, Permission.USER_DELETE)
        
        # Create viewer user
        viewer_user = User(
            username="viewer",
            role="viewer",
            permissions=[]
        )
        
        # Test viewer permissions
        assert PermissionChecker.has_permission(viewer_user, Permission.IOC_READ)
        assert not PermissionChecker.has_permission(viewer_user, Permission.IOC_WRITE)
        assert not PermissionChecker.has_permission(viewer_user, Permission.SYSTEM_CONFIG_WRITE)
        
        # Test custom permissions
        viewer_user.permissions = [Permission.IOC_WRITE]  # type: ignore
        user_perms = PermissionChecker.get_user_permissions(viewer_user)
        assert Permission.IOC_READ in user_perms
        assert Permission.IOC_WRITE in user_perms  # Custom permission added
    
    def test_permission_checking_methods(self):
        """Test various permission checking methods"""
        user = User(
            username="analyst",
            role="analyst",
            permissions=[]
        )
        
        # Test has_any_permission
        assert PermissionChecker.has_any_permission(
            user, [Permission.IOC_READ, Permission.USER_WRITE]
        )
        assert not PermissionChecker.has_any_permission(
            user, [Permission.USER_WRITE, Permission.SYSTEM_CONFIG_WRITE]
        )
        
        # Test has_all_permissions
        assert PermissionChecker.has_all_permissions(
            user, [Permission.IOC_READ, Permission.IOC_WRITE]
        )
        assert not PermissionChecker.has_all_permissions(
            user, [Permission.IOC_READ, Permission.USER_WRITE]
        )
        
        # Test can_access_resource
        assert PermissionChecker.can_access_resource(user, "ioc", "read")
        assert PermissionChecker.can_access_resource(user, "ioc", "write")
        assert not PermissionChecker.can_access_resource(user, "user", "write")


class TestInputValidation:
    """Test input validation and sanitization"""
    
    def test_validate_ioc(self):
        """Test IoC validation"""
        # Valid IoCs
        assert validate_ioc("192.168.1.1") is True
        assert validate_ioc("example.com") is True
        assert validate_ioc("d41d8cd98f00b204e9800998ecf8427e") is True  # MD5
        assert validate_ioc("da39a3ee5e6b4b0d3255bfef95601890afd80709") is True  # SHA1
        assert validate_ioc("<EMAIL>") is True
        assert validate_ioc("http://example.com") is True
        
        # Invalid IoCs
        assert validate_ioc("") is False
        assert validate_ioc("invalid_format") is False
        assert validate_ioc("999.999.999.999") is False  # Invalid IP
        assert validate_ioc("not_an_email") is False
    
    def test_sanitize_input(self):
        """Test input sanitization"""
        # Test dangerous characters removal
        assert sanitize_input("<script>alert('xss')</script>") == "scriptalert('xss')/script"
        assert sanitize_input('SELECT * FROM users WHERE id="1"') == 'SELECT * FROM users WHERE id=1'
        assert sanitize_input("normal text") == "normal text"
        assert sanitize_input("  whitespace  ") == "whitespace"
        
        # Test null byte removal
        assert sanitize_input("test\x00null") == "testnull"
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Test rate limiter
        identifier = "test_user_123"
        
        # Should allow first few requests
        for i in range(5):
            assert rate_limiter.is_allowed(identifier, limit=10, window_seconds=60) is True
        
        # Should still allow more requests under limit
        for i in range(4):
            assert rate_limiter.is_allowed(identifier, limit=10, window_seconds=60) is True
        
        # Should deny request over limit
        assert rate_limiter.is_allowed(identifier, limit=10, window_seconds=60) is False


class TestSecurityMiddleware:
    """Test security middleware"""
    
    def test_security_headers(self, client: TestClient):
        """Test security headers are added"""
        response = client.get("/health")
        
        # Check security headers
        assert "X-Content-Type-Options" in response.headers
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        assert "X-Frame-Options" in response.headers
        assert response.headers["X-Frame-Options"] == "DENY"
        assert "X-XSS-Protection" in response.headers
        assert "Referrer-Policy" in response.headers
    
    def test_cors_headers(self, client: TestClient):
        """Test CORS headers"""
        # Make an OPTIONS request to test CORS
        response = client.options("/api/v1/auth/login")
        
        # Should have CORS headers
        assert response.status_code in [200, 405]  # Depending on implementation
    
    def test_request_size_limit(self, client: TestClient):
        """Test request size limiting"""
        # This would test the request size middleware
        # For now, just ensure the endpoint exists
        response = client.get("/health")
        assert response.status_code == 200


class TestAuthenticationSecurity:
    """Test authentication security features"""
    
    def test_password_hashing(self, db_session: Session):
        """Test password hashing security"""
        auth_service = AuthService(db_session)
        
        password = "TestPassword123"
        hashed = auth_service.hash_password(password)
        
        # Password should be hashed
        assert hashed != password
        assert len(hashed) > 50  # Bcrypt hashes are long
        assert hashed.startswith("$2b$")  # Bcrypt prefix
        
        # Should verify correctly
        assert auth_service.verify_password(password, hashed) is True
        assert auth_service.verify_password("wrong_password", hashed) is False
    
    def test_failed_login_attempts(self, db_session: Session):
        """Test failed login attempt tracking"""
        auth_service = AuthService(db_session)
        
        # Create user
        from ..models.user import UserCreate
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Test multiple failed attempts
        for i in range(3):
            result = auth_service.authenticate_user("testuser", "wrong_password")
            assert result is None
        
        # Refresh user from database
        db_session.refresh(user)
        assert user.failed_login_attempts == 3  # type: ignore

        # Successful login should reset counter
        result = auth_service.authenticate_user("testuser", "TestPassword123")
        assert result is not None

        db_session.refresh(user)
        assert user.failed_login_attempts == 0  # type: ignore
    
    def test_account_lockout(self, db_session: Session):
        """Test account lockout after too many failed attempts"""
        auth_service = AuthService(db_session)
        
        # Create user
        from ..models.user import UserCreate
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Exceed maximum failed attempts
        from ..config.settings import get_security_settings
        security_settings = get_security_settings()
        
        for i in range(security_settings.max_login_attempts + 1):
            auth_service.authenticate_user("testuser", "wrong_password")
        
        # Account should be locked
        db_session.refresh(user)
        assert user.locked_until is not None
        
        # Even correct password should fail when locked
        with pytest.raises(Exception):  # Should raise AuthenticationError
            auth_service.authenticate_user("testuser", "TestPassword123")
    
    def test_jwt_token_security(self, db_session: Session):
        """Test JWT token security"""
        auth_service = AuthService(db_session)
        
        # Create user
        from ..models.user import UserCreate
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Create token
        token = auth_service.create_access_token(user)
        
        # Token should be valid
        token_data = auth_service.verify_token(token)
        assert token_data["username"] == "testuser"
        assert token_data["user_id"] == user.id
        
        # Modified token should be invalid
        modified_token = token[:-5] + "XXXXX"
        with pytest.raises(Exception):  # Should raise AuthenticationError
            auth_service.verify_token(modified_token)
        
        # Completely invalid token should be invalid
        with pytest.raises(Exception):
            auth_service.verify_token("invalid.token.here")


class TestAPIKeySecurity:
    """Test API key security"""
    
    def test_api_key_generation(self, db_session: Session):
        """Test API key generation security"""
        auth_service = AuthService(db_session)
        
        # Create user
        from ..models.user import UserCreate
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Generate API key
        api_key = auth_service.generate_api_key(user)
        
        # API key should have proper format
        assert api_key.startswith("cti_")
        assert len(api_key) > 40  # Should be long enough
        
        # Should be able to validate the key
        validated_user = auth_service.validate_api_key(api_key)
        assert validated_user is not None
        assert validated_user.id == user.id  # type: ignore
    
    def test_api_key_uniqueness(self, db_session: Session):
        """Test API key uniqueness"""
        auth_service = AuthService(db_session)
        
        # Create two users
        from ..models.user import UserCreate
        user1_data = UserCreate(
            username="user1",
            email="<EMAIL>",
            password="Password123",
            confirm_password="Password123"
        )
        user2_data = UserCreate(
            username="user2",
            email="<EMAIL>",
            password="Password123",
            confirm_password="Password123"
        )
        
        user1 = auth_service.create_user(user1_data)
        user2 = auth_service.create_user(user2_data)
        
        # Generate API keys
        key1 = auth_service.generate_api_key(user1)
        key2 = auth_service.generate_api_key(user2)
        
        # Keys should be different
        assert key1 != key2
        
        # Each key should validate to correct user
        validated_user1 = auth_service.validate_api_key(key1)
        validated_user2 = auth_service.validate_api_key(key2)
        assert validated_user1 is not None
        assert validated_user2 is not None
        assert validated_user1.id == user1.id  # type: ignore
        assert validated_user2.id == user2.id  # type: ignore
