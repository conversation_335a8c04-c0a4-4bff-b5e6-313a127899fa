[tool:pytest]
# Pytest configuration for CTI Dashboard

# Test discovery
testpaths = app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    security: Security tests
    slow: Slow running tests
    external: Tests that require external services

# Asyncio configuration
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Uncomment to enable parallel test execution
# -n auto
