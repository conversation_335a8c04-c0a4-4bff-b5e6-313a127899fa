"""
Threat Actor Schemas - Pydantic models for Cyfirma STIX 2.1 threat actor data
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class ThreatActorType(str, Enum):
    """STIX 2.1 threat actor types"""
    ACTIVIST = "activist"
    COMPETITOR = "competitor"
    CRIME_SYNDICATE = "crime-syndicate"
    CRIMINAL = "criminal"
    HACKER = "hacker"
    INSIDER_ACCIDENTAL = "insider-accidental"
    INSIDER_DISGRUNTLED = "insider-disgruntled"
    NATION_STATE = "nation-state"
    SENSATIONALIST = "sensationalist"
    SPY = "spy"
    TERRORIST = "terrorist"
    UNKNOWN = "unknown"


class PrimaryMotivation(str, Enum):
    """STIX 2.1 primary motivations"""
    ACCIDENTAL = "accidental"
    COERCION = "coercion"
    DOMINANCE = "dominance"
    IDEOLOGY = "ideology"
    NOTORIETY = "notoriety"
    ORGANIZATIONAL_GAIN = "organizational-gain"
    PERSONAL_GAIN = "personal-gain"
    PERSONAL_SATISFACTION = "personal-satisfaction"
    REVENGE = "revenge"
    UNPREDICTABLE = "unpredictable"


class STIXExtensionProperties(BaseModel):
    """STIX extension properties from Cyfirma"""
    origin_of_country: Optional[str] = Field(None, alias="origin-of-country")
    target_technologies: Optional[str] = Field(None, alias="target-technologies")
    target_industries: Optional[str] = Field(None, alias="target-industries")
    target_countries: Optional[str] = Field(None, alias="target-countries")


class STIXExtension(BaseModel):
    """STIX extension definition"""
    extension_type: str = Field(alias="extension_type")
    properties: STIXExtensionProperties


class CyfirmaThreatActor(BaseModel):
    """Cyfirma STIX 2.1 Threat Actor model"""
    type: str = "threat-actor"
    id: str
    name: str
    spec_version: str = Field(alias="spec_version")
    created: datetime
    modified: datetime
    description: str = ""
    threat_actor_types: List[ThreatActorType] = Field(alias="threat_actor_types")
    primary_motivation: Optional[PrimaryMotivation] = Field(None, alias="primary_motivation")
    aliases: List[str] = []
    extensions: Optional[Dict[str, STIXExtension]] = {}

    class Config:
        populate_by_name = True
        use_enum_values = True

    @validator('threat_actor_types', pre=True)
    def validate_threat_actor_types(cls, v):
        if isinstance(v, list):
            return [ThreatActorType(item) if isinstance(item, str) else item for item in v]
        return v


class ProcessedThreatActor(BaseModel):
    """Processed threat actor for internal use"""
    # Core STIX fields
    stix_id: str
    name: str
    description: str
    created: datetime
    modified: datetime
    
    # Classification
    actor_types: List[str]
    primary_motivation: Optional[str]
    aliases: List[str] = []
    
    # Attribution and targeting
    origin_country: Optional[str] = None
    target_countries: List[str] = []
    target_industries: List[str] = []
    target_technologies: List[str] = []
    
    # Metadata
    confidence_score: float = 0.0
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    source: str = "cyfirma"
    
    # Relationships (to be populated by relationship analysis)
    associated_malware: List[str] = []
    associated_campaigns: List[str] = []
    associated_vulnerabilities: List[str] = []
    
    # Search and filtering
    tags: List[str] = []
    severity_level: str = "medium"  # low, medium, high, critical


class ThreatActorSearchRequest(BaseModel):
    """Search request for threat actors"""
    query: Optional[str] = None
    actor_types: Optional[List[str]] = None
    motivations: Optional[List[str]] = None
    origin_countries: Optional[List[str]] = None
    target_countries: Optional[List[str]] = None
    target_industries: Optional[List[str]] = None
    aliases: Optional[List[str]] = None
    severity_levels: Optional[List[str]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = Field(default=50, le=500)
    offset: int = Field(default=0, ge=0)


class ThreatActorSearchResponse(BaseModel):
    """Search response for threat actors"""
    total_count: int
    actors: List[ProcessedThreatActor]
    facets: Dict[str, Dict[str, int]] = {}  # For filtering UI
    query_time_ms: float


class ThreatActorRelationship(BaseModel):
    """Relationship between threat actors and other entities"""
    source_id: str
    target_id: str
    relationship_type: str  # "uses", "targets", "attributed-to", etc.
    confidence: float = 0.0
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    description: Optional[str] = None


class ThreatActorUpdate(BaseModel):
    """Update request for threat actor data"""
    stix_id: str
    tags: Optional[List[str]] = None
    severity_level: Optional[str] = None
    notes: Optional[str] = None
    analyst_assessment: Optional[str] = None


class CyfirmaAPIResponse(BaseModel):
    """Response wrapper for Cyfirma API"""
    success: bool
    data: List[CyfirmaThreatActor] = []
    total_count: int = 0
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    error_message: Optional[str] = None
