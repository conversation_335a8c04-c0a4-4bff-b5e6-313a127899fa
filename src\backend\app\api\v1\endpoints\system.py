"""
System Management API endpoints
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, validator

from ....core.security import get_current_active_user, RequireScopes, api_key_manager
from ....core.exceptions import ConfigurationError, ValidationError
from ....config.settings import get_settings, get_security_settings, get_integration_settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Pydantic models
class ConfigUpdateRequest(BaseModel):
    """Configuration update request"""
    enabled_sources: Optional[List[str]] = None
    llm_service: Optional[str] = None
    enable_subnet_matching: Optional[bool] = None
    enable_passive_scanning: Optional[bool] = None
    enable_watchlist_monitoring: Optional[bool] = None
    enable_ai_analysis: Optional[bool] = None


class APIKeyRequest(BaseModel):
    """API key configuration request"""
    service: str
    api_key: str
    encrypt: bool = True
    label: Optional[str] = None  # User-friendly label for the API key
    description: Optional[str] = None  # Optional description


class APIServiceRequest(BaseModel):
    """API service configuration request"""
    name: str
    label: str
    description: Optional[str] = None
    category: str = "custom"  # ai, threat_intelligence, search_engines, custom
    requires_secret: bool = False  # For services that need both ID and secret

    @validator('name')
    def validate_service_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Service name cannot be empty")
        # Only allow alphanumeric, underscore, and hyphen
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError("Service name can only contain letters, numbers, underscores, and hyphens")
        return v.lower().strip()

    @validator('label')
    def validate_service_label(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Service label cannot be empty")
        if len(v.strip()) > 100:
            raise ValueError("Service label cannot exceed 100 characters")
        return v.strip()

    @validator('category')
    def validate_category(cls, v):
        valid_categories = ['ai', 'threat_intelligence', 'search_engines', 'custom']
        if v not in valid_categories:
            raise ValueError(f"Category must be one of: {', '.join(valid_categories)}")
        return v


class APIKeyResponse(BaseModel):
    """API key response"""
    service: str
    label: Optional[str] = None
    description: Optional[str] = None
    category: str
    has_key: bool
    has_secret: bool = False
    created_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    encrypted: bool = True


class SystemHealthResponse(BaseModel):
    """System health response"""
    status: str
    environment: str
    version: str
    components: Dict[str, str]
    uptime_seconds: Optional[float] = None
    memory_usage: Optional[Dict[str, Any]] = None
    disk_usage: Optional[Dict[str, Any]] = None


# System information endpoints
@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health():
    """Get comprehensive system health information"""
    try:
        settings = get_settings()
        
        # Basic health info
        health_info = {
            "status": "healthy",
            "environment": settings.environment,
            "version": settings.app_version,
            "components": {
                "api": "operational",
                "database": "operational",  # TODO: Add actual DB health check
                "cache": "operational",     # TODO: Add actual cache health check
                "integrations": "operational"  # TODO: Add actual integration health checks
            }
        }
        
        # Add system metrics in non-production environments
        if settings.debug:
            import psutil
            import time
            
            # Memory usage
            memory = psutil.virtual_memory()
            health_info["memory_usage"] = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            }
            
            # Disk usage
            disk = psutil.disk_usage('/')
            health_info["disk_usage"] = {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            }
            
            # Uptime (simplified)
            health_info["uptime_seconds"] = time.time() - psutil.boot_time()
        
        return health_info
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "environment": "unknown",
            "version": "unknown",
            "components": {"error": str(e)}
        }


@router.get("/info", response_model=Dict[str, Any])
async def get_system_info(
    current_user=Depends(RequireScopes("system:read"))
):
    """Get system information"""
    try:
        settings = get_settings()
        security_settings = get_security_settings()
        integration_settings = get_integration_settings()
        
        return {
            "success": True,
            "system_info": {
                "app_name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment,
                "debug_mode": settings.debug,
                "api_prefix": settings.api_prefix,
                "features": {
                    "passive_scanning": settings.enable_passive_scanning,
                    "watchlist_monitoring": settings.enable_watchlist_monitoring,
                    "ai_analysis": settings.enable_ai_analysis,
                    "subnet_matching": settings.enable_subnet_matching
                },
                "security": {
                    "authentication_required": security_settings.require_authentication,
                    "rate_limiting_enabled": security_settings.enable_rate_limiting,
                    "cors_enabled": security_settings.enable_cors,
                    "request_logging_enabled": security_settings.enable_request_logging
                },
                "integrations": {
                    "default_timeout": integration_settings.default_timeout,
                    "max_retries": integration_settings.max_retries,
                    "caching_enabled": integration_settings.enable_caching,
                    "cache_ttl_hours": integration_settings.cache_ttl_hours
                }
            }
        }
        
    except Exception as e:
        logger.error(f"System info error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve system information")


# Configuration management endpoints
@router.get("/config", response_model=Dict[str, Any])
async def get_system_config(
    current_user=Depends(RequireScopes("system:config:read"))
):
    """Get system configuration (sanitized)"""
    try:
        settings = get_settings()
        
        # Return sanitized configuration (no sensitive data)
        sanitized_config = {
            "enabled_sources": settings.enabled_sources,
            "llm_service": settings.llm_service,
            "enable_subnet_matching": settings.enable_subnet_matching,
            "enable_passive_scanning": settings.enable_passive_scanning,
            "enable_watchlist_monitoring": settings.enable_watchlist_monitoring,
            "enable_ai_analysis": settings.enable_ai_analysis,
            "rate_limit_per_minute": settings.rate_limit_per_minute,
            "log_level": settings.log_level,
            "configured_services": api_key_manager.list_configured_services()
        }

        return {
            "success": True,
            "config": sanitized_config
        }
        
    except Exception as e:
        logger.error(f"Get config error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve configuration")


@router.post("/config", response_model=Dict[str, Any])
async def update_system_config(
    config_updates: ConfigUpdateRequest,
    current_user=Depends(RequireScopes("system:config:write"))
):
    """Update system configuration"""
    try:
        settings = get_settings()
        updated_keys = []
        
        # Validate and update configuration
        update_data = config_updates.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            if hasattr(settings, key):
                # Validate specific configurations
                if key == "enabled_sources":
                    valid_sources = ["shodan", "censys", "zoomeye", "cyfirma", "virustotal", "abuseipdb"]
                    if not all(source in valid_sources for source in value):
                        raise ValidationError(f"Invalid sources. Valid options: {valid_sources}")
                
                elif key == "llm_service":
                    valid_services = ["openai", "deepseek", "ollama"]
                    if value not in valid_services:
                        raise ValidationError(f"Invalid LLM service. Valid options: {valid_services}")
                
                # Update the setting
                setattr(settings, key, value)
                updated_keys.append(key)
                logger.info(f"Updated config: {key} = {value} by {current_user.username}")

        return {
            "success": True,
            "message": "Configuration updated successfully",
            "updated_keys": updated_keys,
            "updated_by": current_user.username
        }
        
    except ValidationError as e:
        logger.error(f"Config validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Config update error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update configuration")


# API key management endpoints
@router.post("/api-keys", response_model=Dict[str, Any])
async def set_api_key(
    request: APIKeyRequest,
    current_user=Depends(RequireScopes("system:api-keys:write"))
):
    """Set API key for external service"""
    try:
        # Check if service exists (either default or custom)
        service_info = api_key_manager.get_service_info(request.service)
        if not service_info:
            raise ValidationError(f"Unknown service: {request.service}. Add the service first or use a valid service name.")
        
        # Set the API key
        api_key_manager.set_api_key(
            service=request.service,
            key=request.api_key,
            encrypt=request.encrypt
        )
        
        logger.info(f"API key set for {request.service} by {current_user.username}")
        
        return {
            "success": True,
            "message": f"API key set for {request.service}",
            "service": request.service,
            "encrypted": request.encrypt,
            "set_by": current_user.username
        }
        
    except ValidationError as e:
        logger.error(f"API key validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Set API key error: {e}")
        raise HTTPException(status_code=500, detail="Failed to set API key")


@router.delete("/api-keys/{service}", response_model=Dict[str, Any])
async def remove_api_key(
    service: str,
    current_user=Depends(RequireScopes("system:api-keys:write"))
):
    """Remove API key for external service"""
    try:
        success = api_key_manager.remove_api_key(service)
        
        if not success:
            raise HTTPException(status_code=404, detail=f"No API key found for service: {service}")
        
        logger.info(f"API key removed for {service} by {current_user.username}")
        
        return {
            "success": True,
            "message": f"API key removed for {service}",
            "service": service,
            "removed_by": current_user.username
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Remove API key error: {e}")
        raise HTTPException(status_code=500, detail="Failed to remove API key")


@router.get("/api-keys", response_model=Dict[str, Any])
async def list_configured_services(
    _=Depends(RequireScopes("system:api-keys:read"))
):
    """List services with configured API keys"""
    try:
        configured_services = api_key_manager.list_configured_services()

        return {
            "success": True,
            "configured_services": configured_services,
            "total_count": len(configured_services)
        }

    except Exception as e:
        logger.error(f"List API keys error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list configured services")


@router.get("/services", response_model=Dict[str, Any])
async def list_all_services(
    _=Depends(RequireScopes("system:api-keys:read"))
):
    """List all available services (configured and unconfigured)"""
    try:
        all_services = api_key_manager.list_all_services()
        categories = api_key_manager.get_service_categories()

        return {
            "success": True,
            "services": all_services,
            "categories": categories,
            "total_count": len(all_services)
        }

    except Exception as e:
        logger.error(f"List services error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list services")


@router.post("/services", response_model=Dict[str, Any])
async def add_custom_service(
    request: APIServiceRequest,
    current_user=Depends(RequireScopes("system:api-keys:write"))
):
    """Add a custom API service"""
    try:
        # Check if service already exists
        if api_key_manager.get_service_info(request.name):
            raise ValidationError(f"Service '{request.name}' already exists")

        # Add the custom service
        api_key_manager.add_custom_service(
            name=request.name,
            label=request.label,
            description=request.description,
            category=request.category,
            requires_secret=request.requires_secret
        )

        logger.info(f"Custom service '{request.name}' added by {current_user.username}")

        return {
            "success": True,
            "message": f"Custom service '{request.name}' added successfully",
            "service": {
                "name": request.name,
                "label": request.label,
                "category": request.category,
                "description": request.description,
                "requires_secret": request.requires_secret
            },
            "added_by": current_user.username
        }

    except ValidationError as e:
        logger.error(f"Service validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Add service error: {e}")
        raise HTTPException(status_code=500, detail="Failed to add custom service")


@router.delete("/services/{service_name}", response_model=Dict[str, Any])
async def remove_custom_service(
    service_name: str,
    current_user=Depends(RequireScopes("system:api-keys:write"))
):
    """Remove a custom API service and its keys"""
    try:
        # Check if service exists and is custom
        service_info = api_key_manager.get_service_info(service_name)
        if not service_info:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")

        if not service_info.get('is_custom', False):
            raise HTTPException(status_code=400, detail=f"Cannot remove default service '{service_name}'")

        # Remove the service
        success = api_key_manager.remove_service(service_name)

        if not success:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")

        logger.info(f"Custom service '{service_name}' removed by {current_user.username}")

        return {
            "success": True,
            "message": f"Custom service '{service_name}' removed successfully",
            "service": service_name,
            "removed_by": current_user.username
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Remove service error: {e}")
        raise HTTPException(status_code=500, detail="Failed to remove custom service")


# System statistics endpoints
@router.get("/stats", response_model=Dict[str, Any])
async def get_system_statistics(
    _=Depends(RequireScopes("system:read"))
):
    """Get system usage statistics"""
    try:
        # TODO: Implement actual statistics gathering
        # This would typically query the database for various metrics
        
        stats = {
            "total_iocs": 0,  # TODO: Count from database
            "total_actors": 0,  # TODO: Count from database
            "total_watchlist_items": 0,  # TODO: Count from database
            "total_alerts": 0,  # TODO: Count from database
            "api_calls_today": 0,  # TODO: Count from logs/metrics
            "active_users": 0,  # TODO: Count from sessions
            "system_uptime": "0 days",  # TODO: Calculate actual uptime
            "last_backup": None,  # TODO: Get from backup system
            "database_size": "0 MB",  # TODO: Get actual database size
        }
        
        return {
            "success": True,
            "statistics": stats,
            "generated_at": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
        }
        
    except Exception as e:
        logger.error(f"System stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve system statistics")


# Maintenance endpoints
@router.post("/maintenance/cache/clear", response_model=Dict[str, Any])
async def clear_cache(
    current_user=Depends(RequireScopes("system:maintenance"))
):
    """Clear system cache"""
    try:
        # TODO: Implement actual cache clearing
        # This would clear Redis cache, in-memory caches, etc.
        
        logger.info(f"Cache cleared by {current_user.username}")
        
        return {
            "success": True,
            "message": "Cache cleared successfully",
            "cleared_by": current_user.username
        }
        
    except Exception as e:
        logger.error(f"Clear cache error: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.post("/maintenance/logs/rotate", response_model=Dict[str, Any])
async def rotate_logs(
    current_user=Depends(RequireScopes("system:maintenance"))
):
    """Rotate system logs"""
    try:
        # TODO: Implement actual log rotation
        
        logger.info(f"Log rotation initiated by {current_user.username}")
        
        return {
            "success": True,
            "message": "Log rotation completed",
            "initiated_by": current_user.username
        }
        
    except Exception as e:
        logger.error(f"Log rotation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to rotate logs")
