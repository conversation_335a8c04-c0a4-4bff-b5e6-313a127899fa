#!/usr/bin/env python3
"""
Test script for Cyfirma API integration
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def test_cyfirma_api():
    """Test the Cyfirma API integration"""
    
    try:
        from app.services.cyfirma_service import CyfirmaService
        
        # Your API key
        api_key = "N3gxYJNBzm1OFxnwwMgAItdgLrNffYCU"
        
        print("🔍 Testing Cyfirma API integration...")
        print(f"📋 API Key: {api_key[:10]}...")
        
        async with CyfirmaService(api_key) as cyfirma:
            print("✅ CyfirmaService initialized successfully")
            
            # Test fetching all threat actors
            print("📊 Testing fetch_all_threat_actors...")
            response = await cyfirma.fetch_all_threat_actors()
            
            if response.success:
                print(f"✅ Successfully fetched {len(response.data)} threat actors")
                if response.data:
                    print(f"📝 First actor: {response.data[0].name}")
            else:
                print(f"❌ Error fetching threat actors: {response.error_message}")
            
            # Test searching for a specific actor
            print("🔍 Testing search_threat_actor...")
            search_response = await cyfirma.search_threat_actor("APT29")
            
            if search_response.success:
                print(f"✅ Search successful, found {len(search_response.data)} results")
            else:
                print(f"❌ Search error: {search_response.error_message}")
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're running from the backend directory")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_cyfirma_api())
