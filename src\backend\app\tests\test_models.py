"""
Database model tests
"""

import pytest
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models.ioc import IoC, IoCTag, IoCRelationship
from ..models.actor import ThreatActor, ThreatActorCampaign, ThreatActorRelationship
from ..models.watchlist import WatchlistItem, WatchlistAlert, WatchlistRule
from ..models.user import User, UserSession, UserActivity


class TestIoCModels:
    """Test IoC database models"""
    
    def test_create_ioc(self, db_session: Session):
        """Test creating IoC"""
        ioc = IoC(
            value="***********",
            ioc_type="ip",
            source="test_source",
            confidence=0.8,
            threat_actor="test_actor",
            tags=["malicious", "test"],
            created_by="test_user"
        )
        
        db_session.add(ioc)
        db_session.commit()
        db_session.refresh(ioc)
        
        assert ioc.id is not None
        assert ioc.value == "***********"
        assert ioc.ioc_type == "ip"
        assert ioc.confidence == 0.8
        assert "malicious" in ioc.tags
        assert ioc.created_at is not None
        assert ioc.updated_at is not None
    
    def test_ioc_unique_constraint(self, db_session: Session):
        """Test IoC value uniqueness (if implemented)"""
        ioc1 = IoC(
            value="***********",
            ioc_type="ip",
            source="source1"
        )
        
        ioc2 = IoC(
            value="***********",
            ioc_type="ip",
            source="source2"
        )
        
        db_session.add(ioc1)
        db_session.commit()
        
        db_session.add(ioc2)
        # This should work as we allow duplicate values from different sources
        db_session.commit()
    
    def test_ioc_tag(self, db_session: Session):
        """Test IoC tag model"""
        tag = IoCTag(
            name="malicious",
            description="Indicates malicious activity",
            color="#ff0000",
            category="threat",
            created_by="admin"
        )
        
        db_session.add(tag)
        db_session.commit()
        db_session.refresh(tag)
        
        assert tag.id is not None
        assert tag.name == "malicious"
        assert tag.color == "#ff0000"
        assert tag.usage_count == 0
    
    def test_ioc_relationship(self, db_session: Session):
        """Test IoC relationship model"""
        # Create two IoCs first
        ioc1 = IoC(value="***********", ioc_type="ip", source="test")
        ioc2 = IoC(value="malicious.com", ioc_type="domain", source="test")
        
        db_session.add_all([ioc1, ioc2])
        db_session.commit()
        
        # Create relationship
        relationship = IoCRelationship(
            source_ioc_id=ioc1.id,
            target_ioc_id=ioc2.id,
            relationship_type="related",
            confidence=0.9,
            description="IP resolves to domain",
            created_by="analyst"
        )
        
        db_session.add(relationship)
        db_session.commit()
        db_session.refresh(relationship)
        
        assert relationship.id is not None
        assert relationship.source_ioc_id == ioc1.id
        assert relationship.target_ioc_id == ioc2.id
        assert relationship.relationship_type == "related"


class TestThreatActorModels:
    """Test Threat Actor database models"""
    
    def test_create_threat_actor(self, db_session: Session):
        """Test creating threat actor"""
        actor = ThreatActor(
            name="APT-Test",
            aliases=["TestGroup", "TG-001"],
            description="Test threat actor for unit testing",
            origin_country="Unknown",
            primary_motivation="financial-gain",
            target_industries=["technology", "finance"],
            target_regions=["global"],
            ttps=["T1566.001", "T1059.001"],
            associated_malware=["TestMalware"],
            actor_types=["cybercriminal"],
            sophistication_level="medium",
            severity_level="high",
            confidence_score=0.8,
            created_by="analyst"
        )
        
        db_session.add(actor)
        db_session.commit()
        db_session.refresh(actor)
        
        assert actor.id is not None
        assert actor.name == "APT-Test"
        assert "TestGroup" in actor.aliases
        assert "technology" in actor.target_industries
        assert "T1566.001" in actor.ttps
        assert actor.confidence_score == 0.8
    
    def test_threat_actor_campaign(self, db_session: Session):
        """Test threat actor campaign model"""
        # Create actor first
        actor = ThreatActor(
            name="APT-Test",
            description="Test actor",
            created_by="analyst"
        )
        db_session.add(actor)
        db_session.commit()
        
        # Create campaign
        campaign = ThreatActorCampaign(
            name="Operation TestStorm",
            description="Test campaign for unit testing",
            actor_id=actor.id,
            start_date=datetime.utcnow(),
            status="active",
            targets=["TechCorp", "FinanceInc"],
            objectives=["data_theft", "financial_gain"],
            malware_used=["TestMalware"],
            techniques_used=["T1566.001"],
            impact_level="high",
            victims_count=10,
            created_by="analyst"
        )
        
        db_session.add(campaign)
        db_session.commit()
        db_session.refresh(campaign)
        
        assert campaign.id is not None
        assert campaign.name == "Operation TestStorm"
        assert campaign.actor_id == actor.id
        assert "TechCorp" in campaign.targets
        assert campaign.victims_count == 10
    
    def test_threat_actor_relationship(self, db_session: Session):
        """Test threat actor relationship model"""
        # Create two actors
        actor1 = ThreatActor(name="APT-Test1", description="Test actor 1")
        actor2 = ThreatActor(name="APT-Test2", description="Test actor 2")
        
        db_session.add_all([actor1, actor2])
        db_session.commit()
        
        # Create relationship
        relationship = ThreatActorRelationship(
            source_actor_id=actor1.id,
            target_actor_id=actor2.id,
            relationship_type="affiliated",
            confidence=0.7,
            description="Shared infrastructure and TTPs",
            created_by="analyst"
        )
        
        db_session.add(relationship)
        db_session.commit()
        db_session.refresh(relationship)
        
        assert relationship.id is not None
        assert relationship.source_actor_id == actor1.id
        assert relationship.target_actor_id == actor2.id
        assert relationship.relationship_type == "affiliated"


class TestWatchlistModels:
    """Test Watchlist database models"""
    
    def test_create_watchlist_item(self, db_session: Session):
        """Test creating watchlist item"""
        item = WatchlistItem(
            value="malicious.com",
            item_type="domain",
            description="Known malicious domain",
            tags=["malware", "c2"],
            severity="high",
            confidence=0.9,
            match_type="exact",
            source="threat_intel",
            added_by="analyst"
        )
        
        db_session.add(item)
        db_session.commit()
        db_session.refresh(item)
        
        assert item.id is not None
        assert item.value == "malicious.com"
        assert item.item_type == "domain"
        assert item.severity == "high"
        assert "malware" in item.tags
        assert item.match_count == 0
    
    def test_create_watchlist_alert(self, db_session: Session):
        """Test creating watchlist alert"""
        # Create watchlist item first
        item = WatchlistItem(
            value="malicious.com",
            item_type="domain",
            description="Test domain",
            added_by="analyst"
        )
        db_session.add(item)
        db_session.commit()
        
        # Create alert
        alert = WatchlistAlert(
            watchlist_item_id=item.id,
            matched_value="malicious.com",
            match_type="exact",
            source="dns_logs",
            severity="high",
            confidence=0.9,
            status="open",
            context={"query_time": "2024-01-01T12:00:00Z"},
            created_by="system"
        )
        
        db_session.add(alert)
        db_session.commit()
        db_session.refresh(alert)
        
        assert alert.id is not None
        assert alert.watchlist_item_id == item.id
        assert alert.matched_value == "malicious.com"
        assert alert.severity == "high"
        assert alert.status == "open"
        assert not alert.acknowledged
    
    def test_create_watchlist_rule(self, db_session: Session):
        """Test creating watchlist rule"""
        rule = WatchlistRule(
            name="Suspicious Domain Pattern",
            description="Detects domains with suspicious patterns",
            rule_type="regex",
            conditions={
                "pattern": r".*\.(tk|ml|ga|cf)$",
                "case_sensitive": False
            },
            actions={
                "alert_severity": "medium",
                "auto_block": False
            },
            enabled=True,
            priority=7,
            created_by="admin"
        )
        
        db_session.add(rule)
        db_session.commit()
        db_session.refresh(rule)
        
        assert rule.id is not None
        assert rule.name == "Suspicious Domain Pattern"
        assert rule.rule_type == "regex"
        assert rule.enabled is True
        assert rule.priority == 7
        assert rule.match_count == 0


class TestUserModels:
    """Test User database models"""
    
    def test_create_user(self, db_session: Session):
        """Test creating user"""
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            hashed_password="hashed_password_here",
            role="analyst",
            status="active",
            department="Security",
            organization="TestCorp",
            created_by="admin"
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.role == "analyst"
        assert user.is_active is True
        assert user.failed_login_attempts == 0
    
    def test_user_unique_constraints(self, db_session: Session):
        """Test user unique constraints"""
        user1 = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="password1"
        )
        
        user2 = User(
            username="testuser",  # Same username
            email="<EMAIL>",
            hashed_password="password2"
        )
        
        db_session.add(user1)
        db_session.commit()
        
        db_session.add(user2)
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_create_user_session(self, db_session: Session):
        """Test creating user session"""
        # Create user first
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create session
        session = UserSession(
            session_id="test_session_123",
            user_id=user.id,
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser",
            expires_at=datetime.utcnow(),
            last_activity=datetime.utcnow(),
            login_method="password"
        )
        
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        
        assert session.id is not None
        assert session.session_id == "test_session_123"
        assert session.user_id == user.id
        assert session.is_active is True
    
    def test_create_user_activity(self, db_session: Session):
        """Test creating user activity log"""
        # Create user first
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create activity
        activity = UserActivity(
            user_id=user.id,
            session_id="test_session_123",
            action="login",
            resource="auth",
            method="POST",
            endpoint="/api/v1/auth/login",
            ip_address="*************",
            status_code=200,
            success=True,
            duration_ms=150
        )
        
        db_session.add(activity)
        db_session.commit()
        db_session.refresh(activity)
        
        assert activity.id is not None
        assert activity.user_id == user.id
        assert activity.action == "login"
        assert activity.success is True
        assert activity.duration_ms == 150
