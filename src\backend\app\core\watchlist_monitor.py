"""
Watchlist Monitor - Track high-risk entities and generate alerts
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum

from pydantic import BaseModel


class WatchlistItemType(Enum):
    """Types of watchlist items"""
    IP = "ip"
    DOMAIN = "domain"
    HASH = "hash"
    EMAIL = "email"
    THREAT_ACTOR = "threat_actor"
    MALWARE_FAMILY = "malware_family"


class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class WatchlistItem:
    """Watchlist item data model"""
    id: str
    value: str
    item_type: WatchlistItemType
    description: str
    added_date: datetime
    added_by: str
    tags: List[str]
    severity: AlertSeverity = AlertSeverity.MEDIUM
    active: bool = True
    expiry_date: Optional[datetime] = None
    match_count: int = 0
    last_match: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class WatchlistAlert:
    """Watchlist alert data model"""
    id: str
    watchlist_item_id: str
    matched_value: str
    match_type: str  # 'exact', 'partial', 'related'
    source: str
    timestamp: datetime
    severity: AlertSeverity
    context: Dict[str, Any]
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    notes: str = ""

    def __post_init__(self):
        if self.context is None:
            self.context = {}


class WatchlistMonitor:
    """Main watchlist monitoring class"""

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # In-memory storage for demo (replace with database in production)
        self.watchlist_items: Dict[str, WatchlistItem] = {}
        self.alerts: Dict[str, WatchlistAlert] = {}

        # Matching strategies for different item types
        self.matchers = {
            WatchlistItemType.IP: self._match_ip,
            WatchlistItemType.DOMAIN: self._match_domain,
            WatchlistItemType.HASH: self._match_hash,
            WatchlistItemType.EMAIL: self._match_email,
            WatchlistItemType.THREAT_ACTOR: self._match_threat_actor,
            WatchlistItemType.MALWARE_FAMILY: self._match_malware_family
        }

    def add_watchlist_item(self, item: WatchlistItem) -> bool:
        """Add item to watchlist"""
        try:
            self.watchlist_items[item.id] = item
            self.logger.info(f"Added watchlist item: {item.value} ({item.item_type.value})")
            return True
        except Exception as e:
            self.logger.error(f"Failed to add watchlist item: {e}")
            return False

    def remove_watchlist_item(self, item_id: str) -> bool:
        """Remove item from watchlist"""
        try:
            if item_id in self.watchlist_items:
                del self.watchlist_items[item_id]
                self.logger.info(f"Removed watchlist item: {item_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to remove watchlist item: {e}")
            return False

    def get_active_watchlist_items(self) -> List[WatchlistItem]:
        """Get all active watchlist items"""
        current_time = datetime.utcnow()
        active_items = []

        for item in self.watchlist_items.values():
            if item.active:
                # Check if item has expired
                if item.expiry_date and item.expiry_date < current_time:
                    item.active = False
                    continue
                active_items.append(item)

        return active_items

    async def check_intelligence_data(self, intel_data: Dict[str, Any]) -> List[WatchlistAlert]:
        """Check new intelligence data against watchlist"""
        alerts = []
        active_items = self.get_active_watchlist_items()

        for item in active_items:
            try:
                matcher = self.matchers.get(item.item_type)
                if matcher:
                    matches = await matcher(item, intel_data)
                    for match in matches:
                        alert = self._create_alert(item, match, intel_data)
                        alerts.append(alert)
                        self.alerts[alert.id] = alert

                        # Update watchlist item statistics
                        item.match_count += 1
                        item.last_match = datetime.utcnow()

            except Exception as e:
                self.logger.error(f"Error checking watchlist item {item.id}: {e}")

        return alerts

    async def _match_ip(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match IP addresses in intelligence data"""
        matches = []

        # Check various fields that might contain IP addresses
        ip_fields = ['ip', 'source_ip', 'dest_ip', 'c2_ip', 'ioc_value']

        for field in ip_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str) and value == item.value:
                    matches.append({
                        'field': field,
                        'value': value,
                        'match_type': 'exact'
                    })
                elif isinstance(value, list) and item.value in value:
                    matches.append({
                        'field': field,
                        'value': item.value,
                        'match_type': 'exact'
                    })

        # Check for subnet matches if configured
        if self.config.get('enable_subnet_matching', False):
            subnet_matches = await self._check_subnet_matches(item.value, intel_data)
            matches.extend(subnet_matches)

        return matches

    async def _match_domain(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match domains in intelligence data"""
        matches = []

        domain_fields = ['domain', 'hostname', 'c2_domain', 'ioc_value', 'url']

        for field in domain_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str):
                    # Exact match
                    if value == item.value:
                        matches.append({
                            'field': field,
                            'value': value,
                            'match_type': 'exact'
                        })
                    # Subdomain match
                    elif value.endswith(f".{item.value}"):
                        matches.append({
                            'field': field,
                            'value': value,
                            'match_type': 'subdomain'
                        })
                    # URL contains domain
                    elif field == 'url' and item.value in value:
                        matches.append({
                            'field': field,
                            'value': value,
                            'match_type': 'partial'
                        })

        return matches

    async def _match_hash(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match file hashes in intelligence data"""
        matches = []

        hash_fields = ['md5', 'sha1', 'sha256', 'hash', 'file_hash', 'ioc_value']

        for field in hash_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str) and value.lower() == item.value.lower():
                    matches.append({
                        'field': field,
                        'value': value,
                        'match_type': 'exact'
                    })
                elif isinstance(value, list):
                    for hash_val in value:
                        if isinstance(hash_val, str) and hash_val.lower() == item.value.lower():
                            matches.append({
                                'field': field,
                                'value': hash_val,
                                'match_type': 'exact'
                            })

        return matches

    async def _match_email(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match email addresses in intelligence data"""
        matches = []

        email_fields = ['email', 'sender', 'recipient', 'contact_email', 'ioc_value']

        for field in email_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str) and value.lower() == item.value.lower():
                    matches.append({
                        'field': field,
                        'value': value,
                        'match_type': 'exact'
                    })

        return matches

    async def _match_threat_actor(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match threat actor names in intelligence data"""
        matches = []

        actor_fields = ['threat_actor', 'actor_name', 'attribution', 'group_name']

        for field in actor_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str) and value.lower() == item.value.lower():
                    matches.append({
                        'field': field,
                        'value': value,
                        'match_type': 'exact'
                    })
                elif isinstance(value, list):
                    for actor_name in value:
                        if isinstance(actor_name, str) and actor_name.lower() == item.value.lower():
                            matches.append({
                                'field': field,
                                'value': actor_name,
                                'match_type': 'exact'
                            })

        return matches

    async def _match_malware_family(self, item: WatchlistItem, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Match malware family names in intelligence data"""
        matches = []

        malware_fields = ['malware_family', 'malware_name', 'family', 'variant']

        for field in malware_fields:
            if field in intel_data:
                value = intel_data[field]
                if isinstance(value, str) and value.lower() == item.value.lower():
                    matches.append({
                        'field': field,
                        'value': value,
                        'match_type': 'exact'
                    })
                elif isinstance(value, list):
                    for malware_name in value:
                        if isinstance(malware_name, str) and malware_name.lower() == item.value.lower():
                            matches.append({
                                'field': field,
                                'value': malware_name,
                                'match_type': 'exact'
                            })

        return matches

    async def _check_subnet_matches(self, ip: str, intel_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for subnet-based IP matches"""
        import ipaddress

        matches = []

        try:
            watchlist_ip = ipaddress.ip_address(ip)

            # Check if any IPs in intel data belong to the same subnet
            ip_fields = ['ip', 'source_ip', 'dest_ip', 'c2_ip']

            for field in ip_fields:
                if field in intel_data:
                    value = intel_data[field]
                    if isinstance(value, str):
                        try:
                            intel_ip = ipaddress.ip_address(value)
                            # Check if IPs are in the same /24 subnet
                            if watchlist_ip.version == intel_ip.version:
                                watchlist_network = ipaddress.ip_network(f"{watchlist_ip}/24", strict=False)
                                if intel_ip in watchlist_network:
                                    matches.append({
                                        'field': field,
                                        'value': value,
                                        'match_type': 'subnet'
                                    })
                        except ValueError:
                            continue

        except ValueError:
            # Invalid IP address
            pass

        return matches

    def _create_alert(self, item: WatchlistItem, match: Dict[str, Any], intel_data: Dict[str, Any]) -> WatchlistAlert:
        """Create a watchlist alert"""
        import uuid

        alert_id = str(uuid.uuid4())

        # Determine alert severity based on watchlist item severity and match type
        severity = item.severity
        if match['match_type'] == 'exact':
            # Keep original severity for exact matches
            pass
        elif match['match_type'] in ['partial', 'subdomain', 'subnet']:
            # Lower severity for partial matches
            if severity == AlertSeverity.CRITICAL:
                severity = AlertSeverity.HIGH
            elif severity == AlertSeverity.HIGH:
                severity = AlertSeverity.MEDIUM
            elif severity == AlertSeverity.MEDIUM:
                severity = AlertSeverity.LOW

        alert = WatchlistAlert(
            id=alert_id,
            watchlist_item_id=item.id,
            matched_value=match['value'],
            match_type=match['match_type'],
            source=intel_data.get('source', 'unknown'),
            timestamp=datetime.utcnow(),
            severity=severity,
            context={
                'watchlist_item': {
                    'value': item.value,
                    'type': item.item_type.value,
                    'description': item.description,
                    'tags': item.tags
                },
                'match_details': match,
                'intelligence_data': intel_data
            }
        )

        return alert

    def acknowledge_alert(self, alert_id: str, acknowledged_by: str, notes: str = "") -> bool:
        """Acknowledge an alert"""
        try:
            if alert_id in self.alerts:
                alert = self.alerts[alert_id]
                alert.acknowledged = True
                alert.acknowledged_by = acknowledged_by
                alert.acknowledged_at = datetime.utcnow()
                alert.notes = notes

                self.logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to acknowledge alert: {e}")
            return False

    def get_alerts(self,
                   severity_filter: Optional[AlertSeverity] = None,
                   acknowledged_filter: Optional[bool] = None,
                   limit: int = 100) -> List[WatchlistAlert]:
        """Get alerts with optional filtering"""

        alerts = list(self.alerts.values())

        # Apply filters
        if severity_filter:
            alerts = [a for a in alerts if a.severity == severity_filter]

        if acknowledged_filter is not None:
            alerts = [a for a in alerts if a.acknowledged == acknowledged_filter]

        # Sort by timestamp (newest first)
        alerts.sort(key=lambda x: x.timestamp, reverse=True)

        return alerts[:limit]

    def get_watchlist_statistics(self) -> Dict[str, Any]:
        """Get watchlist monitoring statistics"""
        active_items = self.get_active_watchlist_items()
        all_alerts = list(self.alerts.values())

        stats = {
            'total_watchlist_items': len(self.watchlist_items),
            'active_watchlist_items': len(active_items),
            'total_alerts': len(all_alerts),
            'unacknowledged_alerts': len([a for a in all_alerts if not a.acknowledged]),
            'alerts_by_severity': {
                'critical': len([a for a in all_alerts if a.severity == AlertSeverity.CRITICAL]),
                'high': len([a for a in all_alerts if a.severity == AlertSeverity.HIGH]),
                'medium': len([a for a in all_alerts if a.severity == AlertSeverity.MEDIUM]),
                'low': len([a for a in all_alerts if a.severity == AlertSeverity.LOW])
            },
            'items_by_type': {}
        }

        # Count items by type
        for item_type in WatchlistItemType:
            count = len([item for item in active_items if item.item_type == item_type])
            stats['items_by_type'][item_type.value] = count

        return stats

    async def batch_check_intelligence(self, intel_batch: List[Dict[str, Any]]) -> List[WatchlistAlert]:
        """Check multiple intelligence items against watchlist"""
        all_alerts = []

        for intel_data in intel_batch:
            try:
                alerts = await self.check_intelligence_data(intel_data)
                all_alerts.extend(alerts)
            except Exception as e:
                self.logger.error(f"Error processing intelligence batch item: {e}")

        return all_alerts

    def export_watchlist(self) -> List[Dict[str, Any]]:
        """Export watchlist items for backup or transfer"""
        exported_items = []

        for item in self.watchlist_items.values():
            item_dict = asdict(item)
            # Convert datetime objects to ISO strings
            item_dict['added_date'] = item.added_date.isoformat()
            if item.expiry_date:
                item_dict['expiry_date'] = item.expiry_date.isoformat()
            if item.last_match:
                item_dict['last_match'] = item.last_match.isoformat()

            # Convert enums to strings
            item_dict['item_type'] = item.item_type.value
            item_dict['severity'] = item.severity.value

            exported_items.append(item_dict)

        return exported_items

    def import_watchlist(self, imported_items: List[Dict[str, Any]]) -> int:
        """Import watchlist items from backup or transfer"""
        imported_count = 0

        for item_dict in imported_items:
            try:
                # Convert string dates back to datetime objects
                item_dict['added_date'] = datetime.fromisoformat(item_dict['added_date'])
                if item_dict.get('expiry_date'):
                    item_dict['expiry_date'] = datetime.fromisoformat(item_dict['expiry_date'])
                if item_dict.get('last_match'):
                    item_dict['last_match'] = datetime.fromisoformat(item_dict['last_match'])

                # Convert string enums back to enum objects
                item_dict['item_type'] = WatchlistItemType(item_dict['item_type'])
                item_dict['severity'] = AlertSeverity(item_dict['severity'])

                # Create watchlist item
                item = WatchlistItem(**item_dict)

                if self.add_watchlist_item(item):
                    imported_count += 1

            except Exception as e:
                self.logger.error(f"Failed to import watchlist item: {e}")

        return imported_count


# Example configuration
DEFAULT_CONFIG = {
    'enable_subnet_matching': True,
    'alert_retention_days': 30,
    'max_alerts_per_item': 100
}


async def main():
    """Example usage of Watchlist Monitor"""
    import uuid

    config = DEFAULT_CONFIG.copy()
    monitor = WatchlistMonitor(config)

    # Add some watchlist items
    test_items = [
        WatchlistItem(
            id=str(uuid.uuid4()),
            value="*************",
            item_type=WatchlistItemType.IP,
            description="Suspicious IP from previous incident",
            added_date=datetime.utcnow(),
            added_by="analyst1",
            tags=["malware", "c2"],
            severity=AlertSeverity.HIGH
        ),
        WatchlistItem(
            id=str(uuid.uuid4()),
            value="malicious-domain.com",
            item_type=WatchlistItemType.DOMAIN,
            description="Known C2 domain",
            added_date=datetime.utcnow(),
            added_by="analyst2",
            tags=["c2", "phishing"],
            severity=AlertSeverity.CRITICAL
        )
    ]

    for item in test_items:
        monitor.add_watchlist_item(item)

    # Simulate intelligence data
    intel_data = {
        'source': 'threat_feed',
        'ip': '*************',
        'domain': 'sub.malicious-domain.com',
        'timestamp': datetime.utcnow().isoformat()
    }

    # Check for matches
    alerts = await monitor.check_intelligence_data(intel_data)

    print(f"Generated {len(alerts)} alerts")
    for alert in alerts:
        print(f"Alert: {alert.matched_value} ({alert.match_type}) - Severity: {alert.severity.value}")

    # Get statistics
    stats = monitor.get_watchlist_statistics()
    print(f"Watchlist Statistics: {stats}")


if __name__ == "__main__":
    asyncio.run(main())