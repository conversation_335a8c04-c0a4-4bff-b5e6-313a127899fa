"""
IoC (Indicators of Compromise) Service Layer
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import Depends
from sqlalchemy.orm import Session

from ..models.ioc import IoC, IoCSearchResult
from ..models.base import PaginatedResponse, PaginationParams
from ..core.exceptions import ValidationError, ExternalServiceError
from ..core.security import validate_ioc
from ..services.integrations.base_integration import IntegrationManager

logger = logging.getLogger(__name__)


class IoCService:
    """Service for IoC management and enrichment"""
    
    def __init__(self, db: Session, integration_manager: IntegrationManager):
        self.db = db
        self.integration_manager = integration_manager
    
    async def ingest_ioc(
        self,
        value: str,
        source: str,
        threat_actor: Optional[str] = None,
        malware_family: Optional[str] = None,
        tags: List[str] = None,
        created_by: str = None
    ) -> IoC:
        """Ingest and enrich a single IoC"""
        
        # Validate IoC format
        if not validate_ioc(value):
            raise ValidationError(f"Invalid IoC format: {value}")
        
        # Determine IoC type
        ioc_type = self._determine_ioc_type(value)
        
        # Check if IoC already exists
        existing_ioc = self.db.query(IoC).filter(IoC.value == value).first()
        if existing_ioc:
            # Update existing IoC
            existing_ioc.last_seen = datetime.utcnow()
            if threat_actor:
                existing_ioc.threat_actor = threat_actor
            if malware_family:
                existing_ioc.malware_family = malware_family
            if tags:
                existing_ioc.tags = list(set(existing_ioc.tags + tags))
            
            self.db.commit()
            return existing_ioc
        
        # Create new IoC
        ioc = IoC(
            value=value,
            ioc_type=ioc_type,
            source=source,
            threat_actor=threat_actor,
            malware_family=malware_family,
            tags=tags or [],
            first_seen=datetime.utcnow(),
            last_seen=datetime.utcnow(),
            created_by=created_by
        )
        
        # Enrich IoC
        try:
            enrichment_data = await self._enrich_ioc(ioc)
            ioc.enrichment_data = enrichment_data
            ioc.confidence = self._calculate_confidence(enrichment_data)
        except Exception as e:
            logger.warning(f"Failed to enrich IoC {value}: {e}")
            ioc.confidence = 0.5  # Default confidence
        
        # Save to database
        self.db.add(ioc)
        self.db.commit()
        self.db.refresh(ioc)
        
        logger.info(f"Ingested IoC: {value} (type: {ioc_type}, confidence: {ioc.confidence})")
        return ioc
    
    async def batch_ingest(self, ioc_data_list: List[Dict[str, Any]]) -> List[IoC]:
        """Batch ingest multiple IoCs"""
        ingested_iocs = []
        
        for ioc_data in ioc_data_list:
            try:
                ioc = await self.ingest_ioc(**ioc_data)
                ingested_iocs.append(ioc)
            except Exception as e:
                logger.error(f"Failed to ingest IoC {ioc_data.get('value')}: {e}")
                continue
        
        return ingested_iocs
    
    async def search_iocs(
        self,
        query: Optional[str] = None,
        ioc_type: Optional[str] = None,
        threat_actor: Optional[str] = None,
        malware_family: Optional[str] = None,
        confidence_min: Optional[float] = None,
        limit: int = 50,
        offset: int = 0
    ) -> PaginatedResponse:
        """Search IoCs with filters"""
        
        query_obj = self.db.query(IoC)
        
        # Apply filters
        if query:
            query_obj = query_obj.filter(IoC.value.ilike(f"%{query}%"))
        
        if ioc_type:
            query_obj = query_obj.filter(IoC.ioc_type == ioc_type)
        
        if threat_actor:
            query_obj = query_obj.filter(IoC.threat_actor.ilike(f"%{threat_actor}%"))
        
        if malware_family:
            query_obj = query_obj.filter(IoC.malware_family.ilike(f"%{malware_family}%"))
        
        if confidence_min is not None:
            query_obj = query_obj.filter(IoC.confidence >= confidence_min)
        
        # Get total count
        total = query_obj.count()
        
        # Apply pagination
        iocs = query_obj.offset(offset).limit(limit).all()
        
        return PaginatedResponse.create(
            items=iocs,
            total=total,
            pagination=PaginationParams(page=(offset // limit) + 1, size=limit)
        )
    
    async def get_ioc_by_id(self, ioc_id: int) -> Optional[IoC]:
        """Get IoC by ID"""
        return self.db.query(IoC).filter(IoC.id == ioc_id).first()
    
    async def delete_ioc(self, ioc_id: int, deleted_by: str) -> bool:
        """Delete an IoC"""
        ioc = await self.get_ioc_by_id(ioc_id)
        if not ioc:
            return False
        
        # Soft delete
        ioc.is_active = False
        ioc.updated_by = deleted_by
        self.db.commit()
        
        logger.info(f"Deleted IoC: {ioc.value} (ID: {ioc_id}) by {deleted_by}")
        return True
    
    async def refresh_ioc_enrichment(self, ioc_id: int) -> Dict[str, Any]:
        """Refresh enrichment data for an IoC"""
        ioc = await self.get_ioc_by_id(ioc_id)
        if not ioc:
            raise ValidationError("IoC not found")
        
        # Re-enrich the IoC
        enrichment_data = await self._enrich_ioc(ioc)
        ioc.enrichment_data = enrichment_data
        ioc.confidence = self._calculate_confidence(enrichment_data)
        ioc.updated_at = datetime.utcnow()
        
        self.db.commit()
        
        return enrichment_data
    
    def _determine_ioc_type(self, value: str) -> str:
        """Determine IoC type from value"""
        import re
        
        # IP address
        if re.match(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$', value):
            return 'ip'
        
        # Domain
        if re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$', value):
            return 'domain'
        
        # Hash (MD5, SHA1, SHA256)
        if re.match(r'^[a-fA-F0-9]{32}$', value):
            return 'md5'
        elif re.match(r'^[a-fA-F0-9]{40}$', value):
            return 'sha1'
        elif re.match(r'^[a-fA-F0-9]{64}$', value):
            return 'sha256'
        
        # Email
        if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
            return 'email'
        
        # URL
        if re.match(r'^https?://[^\s/$.?#].[^\s]*$', value):
            return 'url'
        
        return 'unknown'
    
    async def _enrich_ioc(self, ioc: IoC) -> Dict[str, Any]:
        """Enrich IoC with external data sources"""
        enrichment_data = {}
        
        try:
            # Get enrichment based on IoC type
            if ioc.ioc_type == 'ip':
                enrichment_data.update(await self._enrich_ip(ioc.value))
            elif ioc.ioc_type == 'domain':
                enrichment_data.update(await self._enrich_domain(ioc.value))
            elif ioc.ioc_type in ['md5', 'sha1', 'sha256']:
                enrichment_data.update(await self._enrich_hash(ioc.value))
            elif ioc.ioc_type == 'url':
                enrichment_data.update(await self._enrich_url(ioc.value))
            
        except Exception as e:
            logger.error(f"Enrichment failed for {ioc.value}: {e}")
            raise ExternalServiceError("enrichment", f"Failed to enrich IoC: {e}")
        
        return enrichment_data
    
    async def _enrich_ip(self, ip: str) -> Dict[str, Any]:
        """Enrich IP address"""
        enrichment = {}
        
        # VirusTotal
        vt_data = await self.integration_manager.get_virustotal_ip_report(ip)
        if vt_data:
            enrichment['virustotal'] = vt_data
        
        # AbuseIPDB
        abuse_data = await self.integration_manager.get_abuseipdb_report(ip)
        if abuse_data:
            enrichment['abuseipdb'] = abuse_data
        
        # Shodan
        shodan_data = await self.integration_manager.get_shodan_host_info(ip)
        if shodan_data:
            enrichment['shodan'] = shodan_data
        
        return enrichment
    
    async def _enrich_domain(self, domain: str) -> Dict[str, Any]:
        """Enrich domain"""
        enrichment = {}
        
        # VirusTotal
        vt_data = await self.integration_manager.get_virustotal_domain_report(domain)
        if vt_data:
            enrichment['virustotal'] = vt_data
        
        # URLVoid
        urlvoid_data = await self.integration_manager.get_urlvoid_report(domain)
        if urlvoid_data:
            enrichment['urlvoid'] = urlvoid_data
        
        return enrichment
    
    async def _enrich_hash(self, hash_value: str) -> Dict[str, Any]:
        """Enrich file hash"""
        enrichment = {}
        
        # VirusTotal
        vt_data = await self.integration_manager.get_virustotal_file_report(hash_value)
        if vt_data:
            enrichment['virustotal'] = vt_data
        
        # Hybrid Analysis
        hybrid_data = await self.integration_manager.get_hybrid_analysis_report(hash_value)
        if hybrid_data:
            enrichment['hybrid_analysis'] = hybrid_data
        
        return enrichment
    
    async def _enrich_url(self, url: str) -> Dict[str, Any]:
        """Enrich URL"""
        enrichment = {}
        
        # VirusTotal
        vt_data = await self.integration_manager.get_virustotal_url_report(url)
        if vt_data:
            enrichment['virustotal'] = vt_data
        
        return enrichment
    
    def _calculate_confidence(self, enrichment_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on enrichment data"""
        if not enrichment_data:
            return 0.5
        
        confidence = 0.0
        sources = 0
        
        # VirusTotal scoring
        if 'virustotal' in enrichment_data:
            vt_data = enrichment_data['virustotal']
            if 'last_analysis_stats' in vt_data:
                stats = vt_data['last_analysis_stats']
                malicious = stats.get('malicious', 0)
                total = sum(stats.values())
                if total > 0:
                    confidence += (malicious / total)
                    sources += 1
        
        # AbuseIPDB scoring
        if 'abuseipdb' in enrichment_data:
            abuse_data = enrichment_data['abuseipdb']
            confidence_percentage = abuse_data.get('abuseConfidencePercentage', 0)
            confidence += (confidence_percentage / 100)
            sources += 1
        
        # Average confidence from all sources
        if sources > 0:
            confidence = confidence / sources
        else:
            confidence = 0.5
        
        return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1


# Dependency injection
def get_ioc_service(
    db: Session = Depends(get_db),
    integration_manager: IntegrationManager = Depends(get_integration_manager)
) -> IoCService:
    """Get IoC service instance"""
    return IoCService(db, integration_manager)


# Placeholder for database dependency (to be implemented)
def get_db():
    """Get database session (placeholder)"""
    pass


# Placeholder for integration manager dependency (to be implemented)
def get_integration_manager():
    """Get integration manager (placeholder)"""
    pass
