<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Management Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="frontend/css/style.css" rel="stylesheet">
    <link href="frontend/css/themes/dark-professional.css" rel="stylesheet" id="theme-css">
</head>
<body class="theme-dark-professional">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-gear"></i> API Management Test</h1>
                <p class="text-muted">Test the new API management functionality</p>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <button type="button" class="btn btn-primary" onclick="testLoadServices()">
                                <i class="bi bi-cloud-download"></i> Test Load Services
                            </button>
                            <button type="button" class="btn btn-success" onclick="testAddCustomService()">
                                <i class="bi bi-plus-circle"></i> Test Add Custom Service
                            </button>
                            <button type="button" class="btn btn-warning" onclick="testValidation()">
                                <i class="bi bi-shield-check"></i> Test Input Validation
                            </button>
                            <button type="button" class="btn btn-info" onclick="testNotifications()">
                                <i class="bi bi-bell"></i> Test Notifications
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="testConfirmDialog()">
                                <i class="bi bi-question-circle"></i> Test Confirm Dialog
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">Run tests to see results here...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Management Interface -->
        <div id="settings-section" class="content-section">
            <!-- This will be populated by the dashboard -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="frontend/js/app.js"></script>

    <script>
        // Initialize dashboard for testing
        let dashboard;
        
        document.addEventListener('DOMContentLoaded', function() {
            dashboard = new CTIDashboard();
            
            // Override API base URL for testing
            dashboard.apiBaseUrl = 'http://127.0.0.1:8000';
            
            // Load the settings section
            dashboard.loadSettingsSection();
            
            logTestResult('Dashboard initialized successfully', 'success');
        });

        function logTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const icon = {
                'success': 'bi-check-circle-fill text-success',
                'error': 'bi-exclamation-triangle-fill text-danger',
                'warning': 'bi-exclamation-triangle-fill text-warning',
                'info': 'bi-info-circle-fill text-info'
            }[type] || 'bi-info-circle-fill text-info';
            
            const resultHtml = `
                <div class="d-flex align-items-start mb-2">
                    <i class="bi ${icon} me-2 mt-1"></i>
                    <div>
                        <small class="text-muted">[${timestamp}]</small>
                        <span class="ms-2">${message}</span>
                    </div>
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('Run tests to see results')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.insertAdjacentHTML('afterbegin', resultHtml);
            }
        }

        async function testLoadServices() {
            try {
                logTestResult('Testing service loading...', 'info');
                await dashboard.loadApiServices();
                logTestResult('Services loaded successfully', 'success');
                logTestResult(`Loaded ${Object.keys(dashboard.apiServices || {}).length} services`, 'info');
            } catch (error) {
                logTestResult(`Service loading failed: ${error.message}`, 'error');
            }
        }

        function testAddCustomService() {
            try {
                logTestResult('Testing add custom service modal...', 'info');
                dashboard.showAddServiceModal();
                logTestResult('Add service modal opened successfully', 'success');
            } catch (error) {
                logTestResult(`Add service modal failed: ${error.message}`, 'error');
            }
        }

        function testValidation() {
            try {
                logTestResult('Testing input validation...', 'info');
                
                // Test service name validation
                const validationTests = [
                    { input: '', expected: false, test: 'Empty service name' },
                    { input: 'valid-service_123', expected: true, test: 'Valid service name' },
                    { input: 'invalid service!', expected: false, test: 'Invalid characters' },
                    { input: 'a'.repeat(60), expected: false, test: 'Too long service name' }
                ];
                
                validationTests.forEach(test => {
                    const result = dashboard.validateServiceInput(test.input, 'Test Label', 'Test Description', 'custom');
                    const passed = result.isValid === test.expected;
                    logTestResult(`${test.test}: ${passed ? 'PASS' : 'FAIL'}`, passed ? 'success' : 'error');
                });
                
                // Test API key validation
                const apiKeyTests = [
                    { key: '', expected: false, test: 'Empty API key' },
                    { key: 'valid-api-key-123', expected: true, test: 'Valid API key' },
                    { key: '<script>alert("xss")</script>', expected: false, test: 'XSS attempt' },
                    { key: 'a'.repeat(1001), expected: false, test: 'Too long API key' }
                ];
                
                apiKeyTests.forEach(test => {
                    const result = dashboard.validateApiKey(test.key, 'test-service');
                    const passed = result.isValid === test.expected;
                    logTestResult(`${test.test}: ${passed ? 'PASS' : 'FAIL'}`, passed ? 'success' : 'error');
                });
                
            } catch (error) {
                logTestResult(`Validation testing failed: ${error.message}`, 'error');
            }
        }

        function testNotifications() {
            try {
                logTestResult('Testing notification system...', 'info');
                
                dashboard.showNotification('Test success notification', 'success', 3000);
                dashboard.showNotification('Test warning notification', 'warning', 3000);
                dashboard.showNotification('Test error notification', 'error', 3000);
                dashboard.showNotification('Test info notification', 'info', 3000);
                
                logTestResult('All notification types displayed', 'success');
            } catch (error) {
                logTestResult(`Notification testing failed: ${error.message}`, 'error');
            }
        }

        function testConfirmDialog() {
            try {
                logTestResult('Testing confirmation dialog...', 'info');
                
                dashboard.showConfirmDialog(
                    'Test Confirmation',
                    'This is a test confirmation dialog. Click Confirm to test the callback.',
                    () => {
                        logTestResult('Confirmation dialog callback executed', 'success');
                        dashboard.showNotification('Confirmation test completed!', 'success');
                    },
                    () => {
                        logTestResult('Confirmation dialog cancelled', 'info');
                    }
                );
                
                logTestResult('Confirmation dialog opened successfully', 'success');
            } catch (error) {
                logTestResult(`Confirmation dialog testing failed: ${error.message}`, 'error');
            }
        }

        // Test security features
        function testSecurity() {
            try {
                logTestResult('Testing security features...', 'info');
                
                // Test input sanitization
                const maliciousInputs = [
                    '<script>alert("xss")</script>',
                    'javascript:alert("xss")',
                    'onclick="alert(\'xss\')"',
                    '<img src="x" onerror="alert(\'xss\')">'
                ];
                
                maliciousInputs.forEach(input => {
                    const sanitized = dashboard.sanitizeInput(input);
                    const isSafe = !sanitized.includes('<script') && !sanitized.includes('javascript:') && !sanitized.includes('onclick');
                    logTestResult(`Sanitization test: ${isSafe ? 'SAFE' : 'UNSAFE'}`, isSafe ? 'success' : 'error');
                });
                
                // Test rate limiting
                try {
                    for (let i = 0; i < 12; i++) {
                        dashboard.rateLimitApiCalls('test-key', 10, 60000);
                    }
                    logTestResult('Rate limiting test: FAIL (should have thrown error)', 'error');
                } catch (rateLimitError) {
                    logTestResult('Rate limiting test: PASS (correctly blocked)', 'success');
                }
                
            } catch (error) {
                logTestResult(`Security testing failed: ${error.message}`, 'error');
            }
        }

        // Auto-run security tests
        setTimeout(testSecurity, 2000);
    </script>
</body>
</html>
