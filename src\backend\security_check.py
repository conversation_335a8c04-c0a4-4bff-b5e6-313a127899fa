#!/usr/bin/env python3
"""
Security Configuration Validation Script for CTI Dashboard
Run this script to validate your security configuration before deployment.
"""

import os
import sys
import re
from typing import List, Dict, <PERSON><PERSON>

def check_environment_variables() -> List[Tuple[str, str, bool]]:
    """Check critical environment variables"""
    checks = []
    
    # Critical security variables
    secret_key = os.getenv('SECRET_KEY', '')
    if len(secret_key) >= 32:
        checks.append(('SECRET_KEY', 'Properly configured', True))
    else:
        checks.append(('SECRET_KEY', 'Missing or too short (min 32 chars)', False))
    
    # CORS configuration
    allowed_origins = os.getenv('ALLOWED_ORIGINS', '')
    if allowed_origins and '*' not in allowed_origins:
        checks.append(('ALLOWED_ORIGINS', 'Properly configured', True))
    elif '*' in allowed_origins:
        checks.append(('ALLOWED_ORIGINS', 'Contains wildcard - SECURITY RISK', False))
    else:
        checks.append(('ALLOWED_ORIGINS', 'Not configured - using defaults', True))
    
    # API Keys (optional but recommended)
    api_keys = {
        'CYFIRMA_API_KEY': 'Cyfirma threat intelligence',
        'SHODAN_API_KEY': 'Shodan passive scanning',
        'VIRUSTOTAL_API_KEY': 'VirusTotal analysis',
        'CENSYS_API_ID': 'Censys scanning',
        'CENSYS_API_SECRET': 'Censys scanning',
        'ZOOMEYE_API_KEY': 'ZoomEye scanning',
        'ABUSEIPDB_API_KEY': 'AbuseIPDB reputation'
    }
    
    for key, description in api_keys.items():
        value = os.getenv(key, '')
        if value:
            checks.append((key, f'{description} - Configured', True))
        else:
            checks.append((key, f'{description} - Not configured (optional)', True))
    
    return checks

def check_file_security() -> List[Tuple[str, str, bool]]:
    """Check for security issues in files"""
    checks = []
    
    # Check if .env file exists and is not in git
    if os.path.exists('.env'):
        checks.append(('.env file', 'Exists (ensure it\'s in .gitignore)', True))
    else:
        checks.append(('.env file', 'Not found (copy from .env.example)', False))
    
    # Check if .env is in .gitignore
    gitignore_path = '.gitignore'
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            gitignore_content = f.read()
            if '.env' in gitignore_content:
                checks.append(('.gitignore', '.env file properly ignored', True))
            else:
                checks.append(('.gitignore', '.env file NOT ignored - SECURITY RISK', False))
    else:
        checks.append(('.gitignore', 'File not found', False))
    
    return checks

def check_hardcoded_secrets() -> List[Tuple[str, str, bool]]:
    """Check for hardcoded secrets in code"""
    checks = []
    
    # Check main app.py file
    app_py_path = 'backend/app/core/app.py'
    if os.path.exists(app_py_path):
        with open(app_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # Check for hardcoded API keys
            if 'N3gxYJNBzm1OFxnwwMgAItdgLrNffYCU' in content:
                checks.append(('app.py', 'Contains hardcoded Cyfirma API key - CRITICAL', False))
            else:
                checks.append(('app.py', 'No hardcoded Cyfirma API key found', True))
            
            # Check for os.getenv usage
            if 'os.getenv(' in content:
                checks.append(('app.py', 'Uses environment variables properly', True))
            else:
                checks.append(('app.py', 'Not using environment variables', False))
            
            # Check CORS configuration
            if 'allow_origins=["*"]' in content:
                checks.append(('app.py', 'Insecure CORS configuration - CRITICAL', False))
            elif 'ALLOWED_ORIGINS' in content:
                checks.append(('app.py', 'Secure CORS configuration', True))
            else:
                checks.append(('app.py', 'CORS configuration not found', False))
    else:
        checks.append(('app.py', 'File not found', False))
    
    return checks

def validate_cors_origins(origins_str: str) -> bool:
    """Validate CORS origins format"""
    if not origins_str:
        return True  # Empty is OK, will use defaults
    
    origins = [origin.strip() for origin in origins_str.split(',')]
    
    for origin in origins:
        # Check for wildcard
        if '*' in origin:
            return False
        
        # Basic URL validation
        if not re.match(r'^https?://[a-zA-Z0-9.-]+(:[0-9]+)?$', origin):
            return False
    
    return True

def main():
    """Run all security checks"""
    print("🔒 CTI Dashboard Security Configuration Check")
    print("=" * 50)
    
    all_passed = True
    
    # Environment variables check
    print("\n📋 Environment Variables:")
    env_checks = check_environment_variables()
    for name, message, passed in env_checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {name}: {message}")
        if not passed:
            all_passed = False
    
    # File security check
    print("\n📁 File Security:")
    file_checks = check_file_security()
    for name, message, passed in file_checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {name}: {message}")
        if not passed:
            all_passed = False
    
    # Hardcoded secrets check
    print("\n🔍 Code Security:")
    secret_checks = check_hardcoded_secrets()
    for name, message, passed in secret_checks:
        status = "✅" if passed else "❌"
        print(f"  {status} {name}: {message}")
        if not passed:
            all_passed = False
    
    # CORS validation
    print("\n🌐 CORS Configuration:")
    allowed_origins = os.getenv('ALLOWED_ORIGINS', '')
    if validate_cors_origins(allowed_origins):
        print("  ✅ CORS origins: Valid configuration")
    else:
        print("  ❌ CORS origins: Invalid or insecure configuration")
        all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All security checks passed!")
        print("Your CTI Dashboard is configured securely.")
    else:
        print("⚠️  Some security issues found!")
        print("Please fix the issues marked with ❌ before deployment.")
        print("\nFor help, see SECURITY_IMPROVEMENTS.md")
    
    print("\n📚 Next Steps:")
    print("1. Set missing environment variables")
    print("2. Ensure .env file is in .gitignore")
    print("3. Never commit API keys to version control")
    print("4. Use HTTPS in production")
    print("5. Regularly rotate API keys")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
