#!/usr/bin/env python3
"""
Test script for API Management Backend Endpoints
"""

import requests
import json
import sys
from typing import Dict, Any

class APIManagementTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if success else "FAIL"
        result = f"[{status}] {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def test_health_check(self):
        """Test basic health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            success = response.status_code == 200
            self.log_result("Health Check", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_result("Health Check", False, str(e))
            return False
    
    def test_list_services(self):
        """Test listing all available services"""
        try:
            response = self.session.get(f"{self.base_url}/system/services")
            
            if response.status_code == 200:
                data = response.json()
                services_count = len(data.get('services', {}))
                self.log_result("List Services", True, f"Found {services_count} services")
                return True
            else:
                self.log_result("List Services", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("List Services", False, str(e))
            return False
    
    def test_add_custom_service(self):
        """Test adding a custom service"""
        try:
            service_data = {
                "name": "test-service",
                "label": "Test Service",
                "description": "A test service for validation",
                "category": "custom",
                "requires_secret": False
            }
            
            response = self.session.post(
                f"{self.base_url}/system/services",
                json=service_data
            )
            
            if response.status_code == 200:
                self.log_result("Add Custom Service", True, "Service added successfully")
                return True
            else:
                error_msg = response.json().get('detail', f'Status: {response.status_code}')
                self.log_result("Add Custom Service", False, error_msg)
                return False
        except Exception as e:
            self.log_result("Add Custom Service", False, str(e))
            return False
    
    def test_input_validation(self):
        """Test input validation on service creation"""
        test_cases = [
            {
                "name": "test_empty_name",
                "data": {"name": "", "label": "Test", "category": "custom"},
                "should_fail": True
            },
            {
                "name": "test_invalid_chars",
                "data": {"name": "test@service!", "label": "Test", "category": "custom"},
                "should_fail": True
            },
            {
                "name": "test_long_name",
                "data": {"name": "a" * 60, "label": "Test", "category": "custom"},
                "should_fail": True
            },
            {
                "name": "test_invalid_category",
                "data": {"name": "test-service", "label": "Test", "category": "invalid"},
                "should_fail": True
            },
            {
                "name": "test_valid_service",
                "data": {"name": "valid-service", "label": "Valid Service", "category": "custom"},
                "should_fail": False
            }
        ]
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for test_case in test_cases:
            try:
                response = self.session.post(
                    f"{self.base_url}/system/services",
                    json=test_case["data"]
                )
                
                if test_case["should_fail"]:
                    # Should return 400 or similar error
                    success = response.status_code >= 400
                else:
                    # Should return 200
                    success = response.status_code == 200
                
                if success:
                    passed_tests += 1
                
                self.log_result(
                    f"Validation Test: {test_case['name']}", 
                    success, 
                    f"Expected {'failure' if test_case['should_fail'] else 'success'}, got {response.status_code}"
                )
                
            except Exception as e:
                self.log_result(f"Validation Test: {test_case['name']}", False, str(e))
        
        overall_success = passed_tests == total_tests
        self.log_result("Input Validation Tests", overall_success, f"{passed_tests}/{total_tests} passed")
        return overall_success
    
    def test_api_key_management(self):
        """Test API key management endpoints"""
        try:
            # Test setting an API key
            api_key_data = {
                "service": "test-service",
                "api_key": "test-api-key-12345",
                "encrypt": True
            }
            
            response = self.session.post(
                f"{self.base_url}/system/api-keys",
                json=api_key_data
            )
            
            if response.status_code == 200:
                self.log_result("Set API Key", True, "API key set successfully")
                
                # Test listing configured services
                list_response = self.session.get(f"{self.base_url}/system/api-keys")
                if list_response.status_code == 200:
                    data = list_response.json()
                    configured_services = data.get('configured_services', [])
                    has_test_service = 'test-service' in configured_services
                    self.log_result("List API Keys", has_test_service, f"Found {len(configured_services)} configured services")
                    return has_test_service
                else:
                    self.log_result("List API Keys", False, f"Status: {list_response.status_code}")
                    return False
            else:
                error_msg = response.json().get('detail', f'Status: {response.status_code}')
                self.log_result("Set API Key", False, error_msg)
                return False
                
        except Exception as e:
            self.log_result("API Key Management", False, str(e))
            return False
    
    def test_security_features(self):
        """Test security features"""
        security_tests = [
            {
                "name": "XSS Prevention",
                "data": {
                    "name": "test-xss",
                    "label": "<script>alert('xss')</script>",
                    "category": "custom"
                }
            },
            {
                "name": "SQL Injection Prevention",
                "data": {
                    "name": "test-sql",
                    "label": "'; DROP TABLE services; --",
                    "category": "custom"
                }
            }
        ]
        
        passed_tests = 0
        for test in security_tests:
            try:
                response = self.session.post(
                    f"{self.base_url}/system/services",
                    json=test["data"]
                )
                
                # Should either reject the input or sanitize it
                if response.status_code == 400:
                    # Rejected - good
                    passed_tests += 1
                    self.log_result(f"Security Test: {test['name']}", True, "Input rejected")
                elif response.status_code == 200:
                    # Check if input was sanitized
                    data = response.json()
                    service_info = data.get('service', {})
                    label = service_info.get('label', '')
                    
                    # Check if dangerous content was removed
                    is_safe = '<script>' not in label and 'DROP TABLE' not in label
                    if is_safe:
                        passed_tests += 1
                        self.log_result(f"Security Test: {test['name']}", True, "Input sanitized")
                    else:
                        self.log_result(f"Security Test: {test['name']}", False, "Dangerous input not sanitized")
                else:
                    self.log_result(f"Security Test: {test['name']}", False, f"Unexpected status: {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Security Test: {test['name']}", False, str(e))
        
        overall_success = passed_tests == len(security_tests)
        self.log_result("Security Tests", overall_success, f"{passed_tests}/{len(security_tests)} passed")
        return overall_success
    
    def cleanup_test_data(self):
        """Clean up test data"""
        try:
            # Try to remove test services
            test_services = ["test-service", "valid-service", "test-xss", "test-sql"]
            
            for service in test_services:
                try:
                    response = self.session.delete(f"{self.base_url}/system/services/{service}")
                    if response.status_code in [200, 404]:  # 404 is OK if service doesn't exist
                        self.log_result(f"Cleanup: {service}", True, "Removed or not found")
                    else:
                        self.log_result(f"Cleanup: {service}", False, f"Status: {response.status_code}")
                except:
                    pass  # Ignore cleanup errors
                    
        except Exception as e:
            self.log_result("Cleanup", False, str(e))
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("API Management Backend Tests")
        print("=" * 60)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("List Services", self.test_list_services),
            ("Add Custom Service", self.test_add_custom_service),
            ("Input Validation", self.test_input_validation),
            ("API Key Management", self.test_api_key_management),
            ("Security Features", self.test_security_features)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\nRunning {test_name}...")
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                self.log_result(test_name, False, f"Test failed with exception: {e}")
        
        print(f"\nCleaning up test data...")
        self.cleanup_test_data()
        
        print("\n" + "=" * 60)
        print(f"Test Results: {passed}/{total} tests passed")
        print("=" * 60)
        
        return passed == total

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test API Management Backend")
    parser.add_argument("--url", default="http://127.0.0.1:8000", help="Backend URL")
    args = parser.parse_args()
    
    tester = APIManagementTester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
