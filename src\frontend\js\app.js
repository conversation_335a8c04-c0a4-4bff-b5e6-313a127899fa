/**
 * CTI Dashboard Frontend Application
 */

class CTIDashboard {
    constructor() {
        console.log('CTI Dashboard initializing...'); // Debug log
        this.apiBaseUrl = 'http://127.0.0.1:8000';
        this.apiKeys = this.loadApiKeys();
        this.currentSection = 'dashboard';

        // Initialize themes first
        this.themes = {
            'light-professional': {
                name: 'Light Professional',
                file: 'css/themes/light-professional.css',
                icon: 'bi-moon-fill'
            },
            'dark-professional': {
                name: 'Dark Professional',
                file: 'css/themes/dark-professional.css',
                icon: 'bi-sun-fill'
            }
        };

        // Then load theme preference
        this.currentTheme = this.loadThemePreference();

        this.init();
    }

    init() {
        console.log('CTI Dashboard init() called'); // Debug log
        // Since we're already called from DOMContentLoaded, we can set up everything directly
        this.setupEventListeners();
        this.initializeTheme();
        this.checkBackendConnection();
        this.loadDashboardDataEnhanced();
        this.setupNavigation();
        this.startRealTimeUpdates();
        this.simulateWebSocketUpdates();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('[data-section]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSection(e.target.dataset.section);
            });
        });

        // IoC Form
        const iocForm = document.getElementById('ioc-form');
        if (iocForm) {
            iocForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.analyzeIoC();
            });
        }

        // Threat Actor Form
        const actorForm = document.getElementById('actor-form');
        if (actorForm) {
            actorForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.analyzeThreatActor();
            });
        }

        // Cyfirma Search Form
        const cyfirmaSearchForm = document.getElementById('cyfirma-search-form');
        if (cyfirmaSearchForm) {
            cyfirmaSearchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performCyfirmaSearch();
            });
        }

        // Enhanced search input with keyboard shortcuts
        const actorNameInput = document.getElementById('cyfirma-actor-name');
        if (actorNameInput) {
            // Enter key to search
            actorNameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performCyfirmaSearch();
                }
            });

            // Clear search on Escape key
            actorNameInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.clearActorSearch();
                }
            });

            // Auto-trim whitespace on input
            actorNameInput.addEventListener('input', (e) => {
                e.target.value = e.target.value.replace(/^\s+/, ''); // Remove leading whitespace
            });
        }

        // Passive Scan Form
        const passiveScanForm = document.getElementById('passive-scan-form');
        if (passiveScanForm) {
            passiveScanForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startPassiveScan();
            });
        }

        // Watchlist Form
        const watchlistForm = document.getElementById('watchlist-form');
        if (watchlistForm) {
            watchlistForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addWatchlistItem();
            });
        }

        // Theme Toggle Button
        const themeToggle = document.getElementById('theme-toggle');
        console.log('Theme toggle button found:', themeToggle); // Debug log
        if (themeToggle) {
            // Remove any existing event listeners
            themeToggle.onclick = null;

            // Add event listener
            themeToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Theme toggle button clicked!'); // Debug log
                this.toggleTheme();
            });
            console.log('Theme toggle event listener attached'); // Debug log
        } else {
            console.error('Theme toggle button not found!');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.stopRealTimeUpdates();
        });
    }

    setupNavigation() {
        // Set active navigation item
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
            this.setupNavigation();

            // Load section-specific data
            switch (sectionName) {
                case 'dashboard':
                    this.loadDashboardDataEnhanced();
                    break;
                case 'actor':
                    // Initialize threat statistics when actor section is first loaded
                    setTimeout(() => {
                        const statsTab = document.getElementById('stats-tab');
                        if (statsTab && !statsTab.dataset.loaded) {
                            this.loadThreatStatistics();
                            statsTab.dataset.loaded = 'true';
                        }
                    }, 500);
                    break;
                case 'watchlist':
                    this.loadWatchlistData();
                    break;
                case 'settings':
                    this.loadSettingsSection();
                    break;
            }
        }
    }

    async checkBackendConnection() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            const data = await response.json();
            
            const statusElement = document.getElementById('connection-status');
            if (response.ok && data.status === 'healthy') {
                statusElement.innerHTML = '<i class="bi bi-circle-fill text-success"></i> Connected';
            } else {
                statusElement.innerHTML = '<i class="bi bi-circle-fill text-warning"></i> Degraded';
            }
        } catch (error) {
            console.error('Backend connection error:', error);
            const statusElement = document.getElementById('connection-status');
            statusElement.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> Disconnected';
        }
    }

    async loadDashboardData() {
        try {
            // Load watchlist stats
            const watchlistResponse = await fetch(`${this.apiBaseUrl}/watchlist/stats`);
            if (watchlistResponse.ok) {
                const watchlistData = await watchlistResponse.json();
                if (watchlistData.success) {
                    document.getElementById('watchlist-count').textContent = 
                        watchlistData.statistics.total_items || 0;
                }
            }

            // Load alerts
            const alertsResponse = await fetch(`${this.apiBaseUrl}/watchlist/alerts?acknowledged=false&limit=5`);
            if (alertsResponse.ok) {
                const alertsData = await alertsResponse.json();
                if (alertsData.success) {
                    document.getElementById('alerts-count').textContent = alertsData.total_alerts || 0;
                    this.displayLatestAlerts(alertsData.alerts || []);
                }
            }

            // Update system status
            const healthResponse = await fetch(`${this.apiBaseUrl}/health`);
            if (healthResponse.ok) {
                const healthData = await healthResponse.json();
                document.getElementById('system-status').textContent = 
                    healthData.status === 'healthy' ? 'Healthy' : 'Degraded';
            }

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    displayLatestAlerts(alerts) {
        const alertsContainer = document.getElementById('latest-alerts');
        
        if (alerts.length === 0) {
            alertsContainer.innerHTML = '<p class="text-muted">No recent alerts</p>';
            return;
        }

        const alertsHtml = alerts.map(alert => `
            <div class="alert alert-${this.getSeverityClass(alert.severity)} alert-dismissible fade show" role="alert">
                <strong>${alert.severity.toUpperCase()}</strong>
                <br>
                <small>${alert.matched_value}</small>
                <br>
                <small class="text-muted">${new Date(alert.timestamp).toLocaleString()}</small>
            </div>
        `).join('');

        alertsContainer.innerHTML = alertsHtml;
    }

    getSeverityClass(severity) {
        const severityMap = {
            'low': 'info',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'danger'
        };
        return severityMap[severity.toLowerCase()] || 'info';
    }

    async analyzeIoC() {
        const form = document.getElementById('ioc-form');
        const resultsContainer = document.getElementById('ioc-results');
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Analyzing...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing IoC...</p></div>';

        try {
            const iocData = {
                value: document.getElementById('ioc-value').value,
                source: document.getElementById('ioc-source').value,
                threat_actor: document.getElementById('threat-actor').value || null,
                malware_family: document.getElementById('malware-family').value || null,
                tags: document.getElementById('ioc-tags').value ? 
                      document.getElementById('ioc-tags').value.split(',').map(tag => tag.trim()) : []
            };

            const response = await fetch(`${this.apiBaseUrl}/ioc/ingest`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(iocData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayIoCResults(result.ioc);
            } else {
                throw new Error(result.detail || 'Analysis failed');
            }

        } catch (error) {
            console.error('IoC analysis error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Analysis Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> Analyze IoC';
        }
    }

    displayIoCResults(ioc) {
        const resultsContainer = document.getElementById('ioc-results');
        
        const confidenceClass = ioc.confidence >= 0.8 ? 'success' : 
                               ioc.confidence >= 0.5 ? 'warning' : 'danger';
        
        const tagsHtml = ioc.tags.map(tag => 
            `<span class="badge bg-secondary">${tag}</span>`
        ).join(' ');

        const enrichmentHtml = ioc.enrichment_sources.map(source => 
            `<span class="badge bg-info">${source}</span>`
        ).join(' ');

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-bug"></i> IoC Analysis Results</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Value:</strong> ${ioc.value}</p>
                        <p><strong>Type:</strong> ${ioc.type}</p>
                        <p><strong>Confidence:</strong> 
                            <span class="badge bg-${confidenceClass}">${(ioc.confidence * 100).toFixed(1)}%</span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Threat Actor:</strong> ${ioc.threat_actor || 'Unknown'}</p>
                        <p><strong>Malware Family:</strong> ${ioc.malware_family || 'Unknown'}</p>
                    </div>
                </div>
                ${tagsHtml ? `<p><strong>Tags:</strong> ${tagsHtml}</p>` : ''}
                ${enrichmentHtml ? `<p><strong>Enrichment Sources:</strong> ${enrichmentHtml}</p>` : ''}
            </div>
        `;
    }

    // API Key Management
    loadApiKeys() {
        const stored = localStorage.getItem('cti_api_keys');
        return stored ? JSON.parse(stored) : {
            gemini: '',
            deepseek: '',
            abuseipdb: '',
            virustotal: '',
            shodan: '',
            censys_id: '',
            censys_secret: '',
            zoomeye: ''
        };
    }

    saveApiKeys() {
        localStorage.setItem('cti_api_keys', JSON.stringify(this.apiKeys));
    }

    async loadSettingsSection() {
        const settingsSection = document.getElementById('settings-section');

        // Show loading state
        settingsSection.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-gear"></i> API Configuration</h2>
                    <p class="text-muted">Configure your API keys for various threat intelligence services. Keys are stored securely.</p>
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading API services...</p>
                    </div>
                </div>
            </div>
        `;

        try {
            // Load all available services from backend
            await this.loadApiServices();
        } catch (error) {
            console.error('Failed to load API services:', error);
            this.showNotification('Failed to load API services. Using local configuration.', 'warning');
            this.renderApiManagementInterface();
        }
    }

    async loadApiServices() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/system/services`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const result = await response.json();
                this.apiServices = result.services || {};
                this.serviceCategories = result.categories || [];
            } else {
                throw new Error('Failed to fetch services');
            }
        } catch (error) {
            console.error('Error loading API services:', error);
            // Fallback to default services
            this.apiServices = this.getDefaultServices();
            this.serviceCategories = ['ai', 'threat_intelligence', 'search_engines', 'custom'];
        }

        this.renderApiManagementInterface();
    }

    getDefaultServices() {
        return {
            'gemini': {
                label: 'Google Gemini',
                category: 'ai',
                description: 'Google Gemini API for AI-powered analysis',
                requires_secret: false,
                has_key: !!this.apiKeys.gemini
            },
            'deepseek': {
                label: 'DeepSeek',
                category: 'ai',
                description: 'DeepSeek API for AI-powered analysis',
                requires_secret: false,
                has_key: !!this.apiKeys.deepseek
            },
            'abuseipdb': {
                label: 'AbuseIPDB',
                category: 'threat_intelligence',
                description: 'AbuseIPDB for IP reputation checking',
                requires_secret: false,
                has_key: !!this.apiKeys.abuseipdb
            },
            'virustotal': {
                label: 'VirusTotal',
                category: 'threat_intelligence',
                description: 'VirusTotal API for malware and URL analysis',
                requires_secret: false,
                has_key: !!this.apiKeys.virustotal
            },
            'shodan': {
                label: 'Shodan',
                category: 'search_engines',
                description: 'Shodan search engine for internet-connected devices',
                requires_secret: false,
                has_key: !!this.apiKeys.shodan
            },
            'censys': {
                label: 'Censys',
                category: 'search_engines',
                description: 'Censys search engine for internet assets',
                requires_secret: true,
                has_key: !!(this.apiKeys.censys_id && this.apiKeys.censys_secret)
            },
            'zoomeye': {
                label: 'ZoomEye',
                category: 'search_engines',
                description: 'ZoomEye search engine for network devices',
                requires_secret: false,
                has_key: !!this.apiKeys.zoomeye
            }
        };
    }

    renderApiManagementInterface() {
        const settingsSection = document.getElementById('settings-section');

        settingsSection.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-gear"></i> API Configuration</h2>
                            <p class="text-muted">Configure your API keys for various threat intelligence services. Keys are stored securely.</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="dashboard.showAddServiceModal()">
                                <i class="bi bi-plus-circle"></i> Add Custom Service
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="dashboard.testAllApiConnections()">
                                <i class="bi bi-wifi"></i> Test All Connections
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Categories Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <ul class="nav nav-pills" id="service-category-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-tab" data-bs-toggle="pill"
                                    data-bs-target="#all-services" type="button" role="tab">
                                <i class="bi bi-grid"></i> All Services
                            </button>
                        </li>
                        ${this.serviceCategories.map(category => `
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="${category}-tab" data-bs-toggle="pill"
                                        data-bs-target="#${category}-services" type="button" role="tab">
                                    <i class="bi bi-${this.getCategoryIcon(category)}"></i> ${this.formatCategoryName(category)}
                                </button>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>

            <!-- Service Cards -->
            <div class="tab-content" id="service-category-content">
                <div class="tab-pane fade show active" id="all-services" role="tabpanel">
                    ${this.renderServiceCards(this.apiServices)}
                </div>
                ${this.serviceCategories.map(category => `
                    <div class="tab-pane fade" id="${category}-services" role="tabpanel">
                        ${this.renderServiceCards(this.filterServicesByCategory(category))}
                    </div>
                `).join('')}
            </div>

            <!-- Bulk Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex gap-2 justify-content-end">
                        <button type="button" class="btn btn-success" onclick="dashboard.saveAllApiConfigurations()">
                            <i class="bi bi-check-circle"></i> Save All Changes
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="dashboard.clearAllApiKeys()">
                            <i class="bi bi-trash"></i> Clear All Keys
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Initialize tooltips
        this.initializeTooltips();
    }

    getCategoryIcon(category) {
        const icons = {
            'ai': 'robot',
            'threat_intelligence': 'shield-check',
            'search_engines': 'search',
            'custom': 'gear'
        };
        return icons[category] || 'gear';
    }

    formatCategoryName(category) {
        const names = {
            'ai': 'AI Services',
            'threat_intelligence': 'Threat Intelligence',
            'search_engines': 'Search Engines',
            'custom': 'Custom Services'
        };
        return names[category] || category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    filterServicesByCategory(category) {
        const filtered = {};
        for (const [serviceName, serviceInfo] of Object.entries(this.apiServices)) {
            if (serviceInfo.category === category) {
                filtered[serviceName] = serviceInfo;
            }
        }
        return filtered;
    }

    renderServiceCards(services) {
        if (Object.keys(services).length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <p class="text-muted mt-3">No services in this category</p>
                </div>
            `;
        }

        return `
            <div class="row">
                ${Object.entries(services).map(([serviceName, serviceInfo]) => `
                    <div class="col-md-6 col-lg-4 mb-4">
                        ${this.renderServiceCard(serviceName, serviceInfo)}
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderServiceCard(serviceName, serviceInfo) {
        const hasKey = serviceInfo.has_key || this.getLocalApiKeyStatus(serviceName);
        const statusClass = hasKey ? 'success' : 'secondary';
        const statusIcon = hasKey ? 'check-circle-fill' : 'circle';
        const statusText = hasKey ? 'Configured' : 'Not Configured';

        return `
            <div class="card api-service-card h-100" data-service="${serviceName}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-${this.getCategoryIcon(serviceInfo.category)} me-2"></i>
                        <h6 class="mb-0">${serviceInfo.label}</h6>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-${statusClass}">
                            <i class="bi bi-${statusIcon}"></i> ${statusText}
                        </span>
                        ${serviceInfo.is_custom ? `
                            <button type="button" class="btn btn-sm btn-outline-danger"
                                    onclick="dashboard.removeCustomService('${serviceName}')"
                                    title="Remove custom service">
                                <i class="bi bi-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted small mb-3">${serviceInfo.description || 'No description available'}</p>

                    ${serviceInfo.requires_secret ? `
                        <!-- Service with ID and Secret -->
                        <div class="mb-3">
                            <label for="${serviceName}-id" class="form-label">API ID</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="${serviceName}-id"
                                       value="${this.getLocalApiKey(serviceName + '_id')}"
                                       placeholder="Enter API ID">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="dashboard.togglePasswordVisibility('${serviceName}-id')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="${serviceName}-secret" class="form-label">API Secret</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="${serviceName}-secret"
                                       value="${this.getLocalApiKey(serviceName + '_secret')}"
                                       placeholder="Enter API Secret">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="dashboard.togglePasswordVisibility('${serviceName}-secret')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                    ` : `
                        <!-- Service with single API Key -->
                        <div class="mb-3">
                            <label for="${serviceName}-key" class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="${serviceName}-key"
                                       value="${this.getLocalApiKey(serviceName)}"
                                       placeholder="Enter API Key">
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="dashboard.togglePasswordVisibility('${serviceName}-key')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                    `}

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary btn-sm flex-fill"
                                onclick="dashboard.saveServiceConfiguration('${serviceName}')">
                            <i class="bi bi-check"></i> Save
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                onclick="dashboard.testServiceConnection('${serviceName}')">
                            <i class="bi bi-wifi"></i> Test
                        </button>
                        ${hasKey ? `
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="dashboard.clearServiceKey('${serviceName}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    getLocalApiKeyStatus(serviceName) {
        if (serviceName === 'censys') {
            return !!(this.apiKeys.censys_id && this.apiKeys.censys_secret);
        }
        return !!this.apiKeys[serviceName];
    }

    getLocalApiKey(keyName) {
        return this.apiKeys[keyName] || '';
    }

    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const button = input.nextElementSibling;
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    }

    async saveServiceConfiguration(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo) {
            this.showNotification(`Service ${serviceName} not found`, 'error');
            return;
        }

        // Get the save button and show loading state
        const saveButton = document.querySelector(`[data-service="${serviceName}"] .btn-primary`);
        this.showLoadingState(saveButton, 'Saving...');

        try {
            let apiKey = '';
            let apiSecret = '';

            if (serviceInfo.requires_secret) {
                const idInput = document.getElementById(`${serviceName}-id`);
                const secretInput = document.getElementById(`${serviceName}-secret`);

                if (!idInput || !secretInput) {
                    throw new Error('Required input fields not found');
                }

                apiKey = idInput.value.trim();
                apiSecret = secretInput.value.trim();

                // Validate API ID
                const idValidation = this.validateApiKey(apiKey, serviceName);
                if (!idValidation.isValid) {
                    throw new Error(`API ID validation failed: ${idValidation.error}`);
                }

                // Validate API Secret
                const secretValidation = this.validateApiKey(apiSecret, `${serviceName}_secret`);
                if (!secretValidation.isValid) {
                    throw new Error(`API Secret validation failed: ${secretValidation.error}`);
                }

                apiKey = idValidation.sanitizedKey;
                apiSecret = secretValidation.sanitizedKey;

                // Store locally for backward compatibility
                this.apiKeys[`${serviceName}_id`] = apiKey;
                this.apiKeys[`${serviceName}_secret`] = apiSecret;

                // Try to save to backend (if available)
                try {
                    await this.saveApiKeyToBackend(serviceName, apiKey, true);
                    if (apiSecret !== apiKey) {
                        await this.saveApiKeyToBackend(`${serviceName}_secret`, apiSecret, true);
                    }
                } catch (backendError) {
                    console.warn('Backend save failed, using local storage:', backendError);
                }
            } else {
                const keyInput = document.getElementById(`${serviceName}-key`);

                if (!keyInput) {
                    throw new Error('API key input field not found');
                }

                apiKey = keyInput.value.trim();

                // Validate API key
                const keyValidation = this.validateApiKey(apiKey, serviceName);
                if (!keyValidation.isValid) {
                    throw new Error(`API key validation failed: ${keyValidation.error}`);
                }

                apiKey = keyValidation.sanitizedKey;

                // Store locally for backward compatibility
                this.apiKeys[serviceName] = apiKey;

                // Try to save to backend (if available)
                try {
                    await this.saveApiKeyToBackend(serviceName, apiKey, true);
                } catch (backendError) {
                    console.warn('Backend save failed, using local storage:', backendError);
                }
            }

            // Save to localStorage as fallback
            this.saveApiKeys();

            // Update service status in UI
            this.updateServiceCardStatus(serviceName, true);

            this.showNotification(`${serviceInfo.label} configuration saved successfully!`, 'success');

        } catch (error) {
            console.error(`Error saving ${serviceName} configuration:`, error);
            this.handleApiError(error, `Saving ${serviceInfo.label} configuration`);
        } finally {
            // Hide loading state
            const saveButton = document.querySelector(`[data-service="${serviceName}"] .btn-primary`);
            this.hideLoadingState(saveButton);
        }
    }

    async saveApiKeyToBackend(serviceName, apiKey, encrypt = true) {
        // Apply rate limiting
        try {
            this.rateLimitApiCalls('save-api-key', 5, 60000); // 5 calls per minute
        } catch (rateLimitError) {
            throw new Error(rateLimitError.message);
        }

        const response = await fetch(`${this.apiBaseUrl}/system/api-keys`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                service: serviceName,
                api_key: apiKey,
                encrypt: encrypt
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to save API key to backend');
        }

        return await response.json();
    }

    updateServiceCardStatus(serviceName, hasKey) {
        const card = document.querySelector(`[data-service="${serviceName}"]`);
        if (!card) return;

        const badge = card.querySelector('.badge');
        const icon = badge.querySelector('i');

        if (hasKey) {
            badge.className = 'badge bg-success';
            icon.className = 'bi bi-check-circle-fill';
            badge.innerHTML = '<i class="bi bi-check-circle-fill"></i> Configured';
        } else {
            badge.className = 'badge bg-secondary';
            icon.className = 'bi bi-circle';
            badge.innerHTML = '<i class="bi bi-circle"></i> Not Configured';
        }
    }

    async testServiceConnection(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo) {
            this.showNotification(`Service ${serviceName} not found`, 'error');
            return;
        }

        const button = document.querySelector(`[data-service="${serviceName}"] .btn-outline-secondary`);
        const originalContent = button.innerHTML;

        try {
            // Show loading state
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Testing...';

            // Simulate API test (replace with actual backend call)
            await new Promise(resolve => setTimeout(resolve, 1500));

            // For now, just check if key exists
            const hasKey = this.getLocalApiKeyStatus(serviceName);

            if (hasKey) {
                this.showNotification(`${serviceInfo.label} connection test successful!`, 'success');
            } else {
                this.showNotification(`${serviceInfo.label} API key not configured`, 'warning');
            }

        } catch (error) {
            console.error(`Error testing ${serviceName} connection:`, error);
            this.showNotification(`${serviceInfo.label} connection test failed: ${error.message}`, 'error');
        } finally {
            // Reset button state
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    }

    clearServiceKey(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo) return;

        this.showConfirmDialog(
            'Clear API Key',
            `Are you sure you want to clear the API key for ${serviceInfo.label}? This action cannot be undone.`,
            () => this.performClearServiceKey(serviceName)
        );
    }

    performClearServiceKey(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo) return;

        try {
            if (serviceInfo.requires_secret) {
                delete this.apiKeys[`${serviceName}_id`];
                delete this.apiKeys[`${serviceName}_secret`];

                const idInput = document.getElementById(`${serviceName}-id`);
                const secretInput = document.getElementById(`${serviceName}-secret`);
                if (idInput) idInput.value = '';
                if (secretInput) secretInput.value = '';
            } else {
                delete this.apiKeys[serviceName];

                const keyInput = document.getElementById(`${serviceName}-key`);
                if (keyInput) keyInput.value = '';
            }

            this.saveApiKeys();
            this.updateServiceCardStatus(serviceName, false);
            this.showNotification(`${serviceInfo.label} API key cleared`, 'info');

        } catch (error) {
            console.error(`Error clearing ${serviceName} key:`, error);
            this.showNotification(`Failed to clear ${serviceInfo.label} API key`, 'error');
        }
    }

    showAddServiceModal() {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="addServiceModal" tabindex="-1" aria-labelledby="addServiceModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addServiceModalLabel">
                                <i class="bi bi-plus-circle"></i> Add Custom Service
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addServiceForm">
                                <div class="mb-3">
                                    <label for="serviceName" class="form-label">Service Name *</label>
                                    <input type="text" class="form-control" id="serviceName" required
                                           placeholder="e.g., my-custom-api" pattern="[a-zA-Z0-9_-]+">
                                    <div class="form-text">Only letters, numbers, underscores, and hyphens allowed</div>
                                </div>
                                <div class="mb-3">
                                    <label for="serviceLabel" class="form-label">Display Label *</label>
                                    <input type="text" class="form-control" id="serviceLabel" required
                                           placeholder="e.g., My Custom API">
                                </div>
                                <div class="mb-3">
                                    <label for="serviceDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="serviceDescription" rows="2"
                                              placeholder="Brief description of the service"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="serviceCategory" class="form-label">Category</label>
                                    <select class="form-select" id="serviceCategory">
                                        <option value="custom">Custom Services</option>
                                        <option value="ai">AI Services</option>
                                        <option value="threat_intelligence">Threat Intelligence</option>
                                        <option value="search_engines">Search Engines</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requiresSecret">
                                        <label class="form-check-label" for="requiresSecret">
                                            Requires separate ID and Secret
                                        </label>
                                        <div class="form-text">Check if this service uses both an API ID and API Secret</div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="dashboard.addCustomService()">
                                <i class="bi bi-plus"></i> Add Service
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('addServiceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addServiceModal'));
        modal.show();
    }

    async addCustomService() {
        // Get and sanitize input values
        const serviceName = this.sanitizeInput(document.getElementById('serviceName').value.trim());
        const serviceLabel = this.sanitizeInput(document.getElementById('serviceLabel').value.trim());
        const serviceDescription = this.sanitizeInput(document.getElementById('serviceDescription').value.trim());
        const serviceCategory = document.getElementById('serviceCategory').value;
        const requiresSecret = document.getElementById('requiresSecret').checked;

        // Comprehensive validation
        const validation = this.validateServiceInput(serviceName, serviceLabel, serviceDescription, serviceCategory);
        if (!validation.isValid) {
            this.showNotification(validation.error, 'error');
            return;
        }

        // Check if service already exists
        if (this.apiServices[serviceName]) {
            this.showNotification(`Service '${serviceName}' already exists`, 'error');
            return;
        }

        try {
            // Try to add to backend first
            try {
                const response = await fetch(`${this.apiBaseUrl}/system/services`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: serviceName,
                        label: serviceLabel,
                        description: serviceDescription,
                        category: serviceCategory,
                        requires_secret: requiresSecret
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Failed to add service to backend');
                }

                // Backend success - reload services
                await this.loadApiServices();

            } catch (backendError) {
                console.warn('Backend add failed, adding locally:', backendError);

                // Fallback to local storage
                this.apiServices[serviceName] = {
                    label: serviceLabel,
                    description: serviceDescription || `Custom ${serviceLabel} service`,
                    category: serviceCategory,
                    requires_secret: requiresSecret,
                    is_custom: true,
                    has_key: false
                };

                // Update categories if needed
                if (!this.serviceCategories.includes(serviceCategory)) {
                    this.serviceCategories.push(serviceCategory);
                }

                // Refresh the interface
                this.renderApiManagementInterface();
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addServiceModal'));
            modal.hide();

            this.showNotification(`Custom service '${serviceLabel}' added successfully!`, 'success');

        } catch (error) {
            console.error('Error adding custom service:', error);
            this.showNotification(`Failed to add custom service: ${error.message}`, 'error');
        }
    }

    async removeCustomService(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo || !serviceInfo.is_custom) {
            this.showNotification('Cannot remove default services', 'error');
            return;
        }

        this.showConfirmDialog(
            'Remove Custom Service',
            `Are you sure you want to remove the custom service '${serviceInfo.label}'? This will also clear any associated API keys and cannot be undone.`,
            () => this.performRemoveCustomService(serviceName)
        );
    }

    async performRemoveCustomService(serviceName) {
        const serviceInfo = this.apiServices[serviceName];
        if (!serviceInfo) return;

        try {
            // Try to remove from backend first
            try {
                const response = await fetch(`${this.apiBaseUrl}/system/services/${serviceName}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || 'Failed to remove service from backend');
                }

                // Backend success - reload services
                await this.loadApiServices();

            } catch (backendError) {
                console.warn('Backend remove failed, removing locally:', backendError);

                // Fallback to local removal
                delete this.apiServices[serviceName];

                // Clear any associated API keys
                if (serviceInfo.requires_secret) {
                    delete this.apiKeys[`${serviceName}_id`];
                    delete this.apiKeys[`${serviceName}_secret`];
                } else {
                    delete this.apiKeys[serviceName];
                }

                this.saveApiKeys();

                // Refresh the interface
                this.renderApiManagementInterface();
            }

            this.showNotification(`Custom service '${serviceInfo.label}' removed successfully`, 'info');

        } catch (error) {
            console.error('Error removing custom service:', error);
            this.showNotification(`Failed to remove custom service: ${error.message}`, 'error');
        }
    }

    async saveAllApiConfigurations() {
        try {
            // Save all current configurations
            this.saveApiKeys();
            this.showNotification('All API configurations saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving all configurations:', error);
            this.showNotification('Failed to save configurations', 'error');
        }
    }

    async clearAllApiKeys() {
        this.showConfirmDialog(
            'Clear All API Keys',
            'Are you sure you want to clear ALL API keys? This action cannot be undone and will remove all configured API credentials.',
            () => this.performClearAllApiKeys()
        );
    }

    async performClearAllApiKeys() {

        try {
            // Clear all API keys
            this.apiKeys = {
                gemini: '',
                deepseek: '',
                abuseipdb: '',
                virustotal: '',
                shodan: '',
                censys_id: '',
                censys_secret: '',
                zoomeye: ''
            };

            this.saveApiKeys();

            // Refresh the interface
            this.renderApiManagementInterface();

            this.showNotification('All API keys cleared successfully', 'info');

        } catch (error) {
            console.error('Error clearing all API keys:', error);
            this.showNotification('Failed to clear API keys', 'error');
        }
    }

    async testAllApiConnections() {
        const configuredServices = Object.entries(this.apiServices).filter(([name]) =>
            this.getLocalApiKeyStatus(name)
        );

        if (configuredServices.length === 0) {
            this.showNotification('No API services are configured for testing', 'warning');
            return;
        }

        this.showNotification(`Testing ${configuredServices.length} configured services...`, 'info');

        // Test each service (simplified - in real app would make actual API calls)
        for (const [serviceName] of configuredServices) {
            try {
                await this.testServiceConnection(serviceName);
                await new Promise(resolve => setTimeout(resolve, 500)); // Delay between tests
            } catch (error) {
                console.error(`Failed to test ${serviceName}:`, error);
            }
        }
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Security and Validation Methods
    sanitizeInput(input) {
        if (typeof input !== 'string') return '';

        // Remove potentially dangerous characters and scripts
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/on\w+\s*=/gi, '') // Remove event handlers
            .replace(/[<>'"&]/g, function(match) { // Escape HTML entities
                const entities = {
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#x27;',
                    '&': '&amp;'
                };
                return entities[match];
            })
            .trim();
    }

    validateServiceInput(serviceName, serviceLabel, serviceDescription, serviceCategory) {
        // Service name validation
        if (!serviceName || serviceName.length === 0) {
            return { isValid: false, error: 'Service name is required' };
        }

        if (serviceName.length > 50) {
            return { isValid: false, error: 'Service name cannot exceed 50 characters' };
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(serviceName)) {
            return { isValid: false, error: 'Service name can only contain letters, numbers, underscores, and hyphens' };
        }

        // Service label validation
        if (!serviceLabel || serviceLabel.length === 0) {
            return { isValid: false, error: 'Service label is required' };
        }

        if (serviceLabel.length > 100) {
            return { isValid: false, error: 'Service label cannot exceed 100 characters' };
        }

        // Description validation (optional but limited)
        if (serviceDescription && serviceDescription.length > 500) {
            return { isValid: false, error: 'Service description cannot exceed 500 characters' };
        }

        // Category validation
        const validCategories = ['ai', 'threat_intelligence', 'search_engines', 'custom'];
        if (!validCategories.includes(serviceCategory)) {
            return { isValid: false, error: 'Invalid service category' };
        }

        return { isValid: true };
    }

    validateApiKey(apiKey, serviceName) {
        if (!apiKey || typeof apiKey !== 'string') {
            return { isValid: false, error: 'API key is required' };
        }

        // Remove whitespace
        apiKey = apiKey.trim();

        if (apiKey.length === 0) {
            return { isValid: false, error: 'API key cannot be empty' };
        }

        if (apiKey.length > 1000) {
            return { isValid: false, error: 'API key is too long (max 1000 characters)' };
        }

        // Check for potentially dangerous content
        if (/<script|javascript:|on\w+\s*=/i.test(apiKey)) {
            return { isValid: false, error: 'API key contains invalid characters' };
        }

        // Service-specific validation
        if (serviceName === 'virustotal' && apiKey.length !== 64) {
            return { isValid: false, error: 'VirusTotal API keys should be 64 characters long' };
        }

        if (serviceName === 'shodan' && apiKey.length !== 32) {
            return { isValid: false, error: 'Shodan API keys should be 32 characters long' };
        }

        return { isValid: true, sanitizedKey: apiKey };
    }

    // Enhanced error handling
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);

        let userMessage = 'An unexpected error occurred';

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            userMessage = 'Network error: Unable to connect to the server';
        } else if (error.message) {
            // Sanitize error message before showing to user
            userMessage = this.sanitizeInput(error.message);
        }

        this.showNotification(`${context ? context + ': ' : ''}${userMessage}`, 'error');
    }

    // Rate limiting for API calls
    rateLimitApiCalls(key, maxCalls = 10, windowMs = 60000) {
        if (!this.rateLimitStore) {
            this.rateLimitStore = new Map();
        }

        const now = Date.now();
        const windowStart = now - windowMs;

        if (!this.rateLimitStore.has(key)) {
            this.rateLimitStore.set(key, []);
        }

        const calls = this.rateLimitStore.get(key);

        // Remove old calls outside the window
        const recentCalls = calls.filter(timestamp => timestamp > windowStart);

        if (recentCalls.length >= maxCalls) {
            throw new Error(`Rate limit exceeded for ${key}. Please wait before trying again.`);
        }

        recentCalls.push(now);
        this.rateLimitStore.set(key, recentCalls);

        return true;
    }

    // Legacy methods - replaced by new API management system
    saveApiConfiguration() {
        this.saveAllApiConfigurations();
    }

    clearApiKeys() {
        this.clearAllApiKeys();
    }

    showNotification(message, type = 'info', duration = 5000) {
        // Create notification container if it doesn't exist
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'position-fixed';
            container.style.cssText = 'top: 100px; right: 20px; z-index: 1050; max-width: 400px;';
            document.body.appendChild(container);
        }

        // Create notification element
        const notification = document.createElement('div');
        const notificationId = 'notification-' + Date.now();
        notification.id = notificationId;
        notification.className = `alert alert-${type} alert-dismissible fade show mb-2 shadow-sm`;
        notification.style.cssText = 'animation: slideInRight 0.3s ease-out;';

        // Get appropriate icon for notification type
        const icons = {
            'success': 'bi-check-circle-fill',
            'error': 'bi-exclamation-triangle-fill',
            'warning': 'bi-exclamation-triangle-fill',
            'info': 'bi-info-circle-fill'
        };

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi ${icons[type] || icons.info} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" onclick="dashboard.dismissNotification('${notificationId}')"></button>
            </div>
        `;

        container.appendChild(notification);

        // Auto-remove after specified duration
        if (duration > 0) {
            setTimeout(() => {
                this.dismissNotification(notificationId);
            }, duration);
        }

        return notificationId;
    }

    dismissNotification(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }

    showLoadingState(element, message = 'Loading...') {
        if (!element) return;

        element.classList.add('loading');
        element.setAttribute('data-original-content', element.innerHTML);
        element.innerHTML = `
            <div class="d-flex align-items-center justify-content-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                ${message}
            </div>
        `;
        element.disabled = true;
    }

    hideLoadingState(element) {
        if (!element) return;

        element.classList.remove('loading');
        const originalContent = element.getAttribute('data-original-content');
        if (originalContent) {
            element.innerHTML = originalContent;
            element.removeAttribute('data-original-content');
        }
        element.disabled = false;
    }

    showConfirmDialog(title, message, onConfirm, onCancel = null) {
        const modalHtml = `
            <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="confirmModalLabel">
                                <i class="bi bi-question-circle text-warning me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p class="mb-0">${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="dashboard.handleConfirmCancel()">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="dashboard.handleConfirmOk()">
                                Confirm
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('confirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Store callbacks
        this.confirmCallbacks = { onConfirm, onCancel };

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        modal.show();
    }

    handleConfirmOk() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
        modal.hide();

        if (this.confirmCallbacks && this.confirmCallbacks.onConfirm) {
            this.confirmCallbacks.onConfirm();
        }
    }

    handleConfirmCancel() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
        modal.hide();

        if (this.confirmCallbacks && this.confirmCallbacks.onCancel) {
            this.confirmCallbacks.onCancel();
        }
    }

    // Threat Actor Analysis
    async analyzeThreatActor() {
        const form = document.getElementById('actor-form');
        const resultsContainer = document.getElementById('actor-results');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Analyzing...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing threat actor...</p></div>';

        try {
            const actorData = {
                name: document.getElementById('actor-name').value,
                aliases: document.getElementById('actor-aliases').value ?
                        document.getElementById('actor-aliases').value.split(',').map(a => a.trim()) : [],
                description: document.getElementById('actor-description').value,
                origin_country: document.getElementById('actor-origin').value || null,
                motivation: document.getElementById('actor-motivation').value ?
                           document.getElementById('actor-motivation').value.split(',').map(m => m.trim()) : [],
                target_industries: document.getElementById('actor-industries').value ?
                                  document.getElementById('actor-industries').value.split(',').map(i => i.trim()) : [],
                target_regions: document.getElementById('actor-regions').value ?
                               document.getElementById('actor-regions').value.split(',').map(r => r.trim()) : [],
                ttps: document.getElementById('actor-ttps').value ?
                     document.getElementById('actor-ttps').value.split(',').map(t => t.trim()) : [],
                associated_malware: document.getElementById('actor-malware').value ?
                                   document.getElementById('actor-malware').value.split(',').map(m => m.trim()) : []
            };

            const response = await fetch(`${this.apiBaseUrl}/actor/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(actorData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayActorResults(result);
            } else {
                throw new Error(result.detail || 'Analysis failed');
            }

        } catch (error) {
            console.error('Threat actor analysis error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Analysis Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-cpu"></i> Analyze Actor';
        }
    }

    displayActorResults(result) {
        const resultsContainer = document.getElementById('actor-results');
        const analysis = result.analysis;

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-person-x"></i> ${result.actor.name}</h6>
                <div class="mb-3">
                    <strong>Confidence:</strong>
                    <span class="badge bg-${analysis.confidence_level >= 0.8 ? 'success' : analysis.confidence_level >= 0.5 ? 'warning' : 'danger'}">
                        ${(analysis.confidence_level * 100).toFixed(1)}%
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Executive Summary:</strong>
                    <p class="text-truncate-2">${analysis.executive_summary}</p>
                </div>
                <div class="mb-3">
                    <strong>Risk Assessment:</strong>
                    <p class="text-truncate-2">${analysis.risk_assessment}</p>
                </div>
                <div class="mb-2">
                    <strong>Attack Vectors:</strong>
                    <ul class="list-unstyled">
                        ${analysis.attack_vectors.slice(0, 3).map(vector => `<li>• ${vector}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    // Cyfirma Threat Intelligence Integration
    async searchCyfirmaActors(searchParams = {}) {
        try {
            // Check if this is a simple name-only search
            const isSimpleSearch = searchParams.query &&
                                 !searchParams.actor_types &&
                                 !searchParams.motivations &&
                                 !searchParams.origin_countries &&
                                 !searchParams.target_countries &&
                                 !searchParams.target_industries &&
                                 !searchParams.severity_levels;

            let response;

            if (isSimpleSearch) {
                // Use the simplified endpoint for name-only searches
                const params = new URLSearchParams();
                params.append('name', searchParams.query);
                if (searchParams.limit) params.append('limit', searchParams.limit);
                if (searchParams.offset) params.append('offset', searchParams.offset);

                response = await fetch(`${this.apiBaseUrl}/actor/cyfirma/search/simple?${params}`);
            } else {
                // Use the full search endpoint for complex searches
                const params = new URLSearchParams();

                // Add search parameters
                if (searchParams.query) params.append('query', searchParams.query);
                if (searchParams.actor_types) params.append('actor_types', searchParams.actor_types.join(','));
                if (searchParams.motivations) params.append('motivations', searchParams.motivations.join(','));
                if (searchParams.origin_countries) params.append('origin_countries', searchParams.origin_countries.join(','));
                if (searchParams.target_countries) params.append('target_countries', searchParams.target_countries.join(','));
                if (searchParams.target_industries) params.append('target_industries', searchParams.target_industries.join(','));
                if (searchParams.severity_levels) params.append('severity_levels', searchParams.severity_levels.join(','));
                if (searchParams.limit) params.append('limit', searchParams.limit);
                if (searchParams.offset) params.append('offset', searchParams.offset);

                response = await fetch(`${this.apiBaseUrl}/actor/cyfirma/search?${params}`);
            }

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('Cyfirma search failed:', error);
            return {
                total_count: 0,
                actors: [],
                facets: {},
                query_time_ms: 0
            };
        }
    }

    async getCyfirmaActor(actorName) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/actor/cyfirma/${encodeURIComponent(actorName)}`);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Failed to get Cyfirma actor:', error);
            return { success: false, error: error.message };
        }
    }

    async getCyfirmaStats() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/actor/cyfirma/stats`);
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Failed to get Cyfirma stats:', error);
            return { success: false, error: error.message };
        }
    }

    displayCyfirmaActors(searchResult) {
        const resultsContainer = document.getElementById('cyfirma-results');

        // Handle error responses with demo mode option
        if (searchResult.error) {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Service Notice:</strong> ${searchResult.error}
                    <br><small class="text-muted">The threat actor search service is currently in fallback mode.</small>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="app.showDemoData()">
                            <i class="bi bi-play-circle"></i> View Demo Data
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="app.showDemoStats()">
                            <i class="bi bi-bar-chart"></i> Demo Statistics
                        </button>
                        <br><small class="text-muted">See sample threat actor intelligence and statistics</small>
                    </div>
                </div>
            `;
            return;
        }

        if (!searchResult.actors || searchResult.actors.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No threat actors found matching your criteria.
                    <br><small class="text-muted">Try adjusting your search terms or filters.</small>
                </div>
            `;
            return;
        }

        const actorsHtml = searchResult.actors.map(actor => `
            <div class="card mb-3 threat-actor-card" data-actor-id="${actor.stix_id}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-person-x"></i> ${actor.name}
                        <span class="badge bg-${this.getSeverityColor(actor.severity_level)} ms-2">
                            ${actor.severity_level.toUpperCase()}
                        </span>
                    </h6>
                    <small class="text-muted">Confidence: ${(actor.confidence_score * 100).toFixed(1)}%</small>
                </div>
                <div class="card-body">
                    <p class="card-text">${actor.description.substring(0, 200)}${actor.description.length > 200 ? '...' : ''}</p>

                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Origin:</strong> ${actor.origin_country || 'Unknown'}<br>
                                <strong>Motivation:</strong> ${actor.primary_motivation || 'Unknown'}<br>
                                <strong>Types:</strong> ${actor.actor_types.join(', ')}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Aliases:</strong> ${actor.aliases.slice(0, 3).join(', ')}${actor.aliases.length > 3 ? '...' : ''}<br>
                                <strong>Target Industries:</strong> ${actor.target_industries.slice(0, 2).join(', ')}${actor.target_industries.length > 2 ? '...' : ''}
                            </small>
                        </div>
                    </div>

                    <div class="mt-2">
                        ${actor.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                    </div>

                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="dashboard.viewActorDetails('${actor.name}')">
                            <i class="bi bi-eye"></i> View Details
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="dashboard.addToWatchlist('${actor.name}', 'threat_actor')">
                            <i class="bi bi-plus-circle"></i> Add to Watchlist
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        resultsContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>Found ${searchResult.total_count} threat actors (${searchResult.query_time_ms.toFixed(1)}ms)</h6>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="app.exportThreatActorData('json')">
                        <i class="bi bi-download"></i> JSON
                    </button>
                    <button class="btn btn-outline-secondary" onclick="app.exportThreatActorData('csv')">
                        <i class="bi bi-file-earmark-spreadsheet"></i> CSV
                    </button>
                </div>
            </div>
            ${actorsHtml}
        `;
    }

    getSeverityColor(severity) {
        const colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'dark'
        };
        return colors[severity] || 'secondary';
    }

    showDemoData() {
        const demoData = {
            total_count: 5,
            actors: [
                {
                    stix_id: "intrusion-set--demo-001",
                    name: "APT29 (Cozy Bear)",
                    description: "Advanced persistent threat group associated with Russian intelligence services. Known for sophisticated spear-phishing campaigns and supply chain attacks.",
                    created: "2023-01-15T10:30:00Z",
                    modified: "2024-12-01T14:22:00Z",
                    actor_types: ["nation-state"],
                    primary_motivation: "espionage",
                    aliases: ["Cozy Bear", "The Dukes", "Group 100"],
                    origin_country: "Russia",
                    target_countries: ["United States", "United Kingdom", "Germany", "France"],
                    target_industries: ["Government", "Healthcare", "Technology", "Energy"],
                    confidence_score: 0.95,
                    severity_level: "high",
                    source: "demo"
                },
                {
                    stix_id: "intrusion-set--demo-002",
                    name: "Lazarus Group",
                    description: "North Korean state-sponsored cyber group known for financial crimes, cryptocurrency theft, and destructive attacks against critical infrastructure.",
                    created: "2023-02-20T09:15:00Z",
                    modified: "2024-11-28T16:45:00Z",
                    actor_types: ["nation-state"],
                    primary_motivation: "financial-gain",
                    aliases: ["HIDDEN COBRA", "Guardians of Peace", "Whois Team"],
                    origin_country: "North Korea",
                    target_countries: ["South Korea", "United States", "Japan", "Global"],
                    target_industries: ["Financial Services", "Cryptocurrency", "Entertainment", "Government"],
                    confidence_score: 0.92,
                    severity_level: "high",
                    source: "demo"
                },
                {
                    stix_id: "intrusion-set--demo-003",
                    name: "FIN7",
                    description: "Financially motivated cybercriminal group specializing in point-of-sale malware and payment card theft from retail and hospitality sectors.",
                    created: "2023-03-10T11:20:00Z",
                    modified: "2024-10-15T13:30:00Z",
                    actor_types: ["crime-syndicate"],
                    primary_motivation: "financial-gain",
                    aliases: ["Carbanak Group", "Navigator Group"],
                    origin_country: "Unknown",
                    target_countries: ["United States", "Canada", "United Kingdom", "Australia"],
                    target_industries: ["Retail", "Hospitality", "Financial Services"],
                    confidence_score: 0.88,
                    severity_level: "medium",
                    source: "demo"
                },
                {
                    stix_id: "intrusion-set--demo-004",
                    name: "Equation Group",
                    description: "Highly sophisticated threat actor with advanced capabilities and access to zero-day exploits. Suspected to be affiliated with intelligence agencies.",
                    created: "2023-04-05T08:45:00Z",
                    modified: "2024-09-22T12:10:00Z",
                    actor_types: ["nation-state"],
                    primary_motivation: "espionage",
                    aliases: ["EQGRP", "Tailored Access Operations"],
                    origin_country: "United States",
                    target_countries: ["Iran", "Russia", "China", "Global"],
                    target_industries: ["Government", "Military", "Telecommunications", "Energy"],
                    confidence_score: 0.90,
                    severity_level: "high",
                    source: "demo"
                },
                {
                    stix_id: "intrusion-set--demo-005",
                    name: "DarkHalo",
                    description: "Emerging threat group using novel techniques for initial access and persistence. Known for targeting cloud infrastructure and SaaS applications.",
                    created: "2024-01-12T14:30:00Z",
                    modified: "2024-12-01T09:15:00Z",
                    actor_types: ["unknown"],
                    primary_motivation: "espionage",
                    aliases: ["UNC2452", "StellarParticle"],
                    origin_country: "Unknown",
                    target_countries: ["United States", "Europe", "Asia-Pacific"],
                    target_industries: ["Technology", "Government", "Consulting"],
                    confidence_score: 0.75,
                    severity_level: "medium",
                    source: "demo"
                }
            ],
            facets: {},
            query_time_ms: 125.5
        };

        this.displayCyfirmaActors(demoData);

        // Show demo notice
        const resultsContainer = document.getElementById('cyfirma-results');
        const demoNotice = `
            <div class="alert alert-info mb-3">
                <i class="bi bi-info-circle"></i>
                <strong>Demo Mode:</strong> Displaying sample threat actor data for demonstration purposes.
                <button class="btn btn-outline-secondary btn-sm float-end" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
        `;
        resultsContainer.innerHTML = demoNotice + resultsContainer.innerHTML;
    }

    showDemoStats() {
        const demoStats = {
            success: true,
            stats: {
                total_actors: 247,
                last_updated: new Date().toISOString(),
                actor_types: {
                    "nation-state": 89,
                    "crime-syndicate": 76,
                    "hacktivist": 45,
                    "unknown": 37
                },
                motivations: {
                    "espionage": 112,
                    "financial-gain": 89,
                    "ideology": 28,
                    "notoriety": 18
                },
                origin_countries: {
                    "Russia": 45,
                    "China": 38,
                    "North Korea": 22,
                    "Iran": 19,
                    "United States": 15,
                    "Unknown": 108
                },
                severity_distribution: {
                    "high": 98,
                    "medium": 124,
                    "low": 25
                },
                top_target_industries: {
                    "Government": 156,
                    "Financial Services": 134,
                    "Technology": 98,
                    "Healthcare": 76,
                    "Energy": 67,
                    "Retail": 45,
                    "Education": 34,
                    "Manufacturing": 28
                },
                top_target_countries: {
                    "United States": 189,
                    "United Kingdom": 87,
                    "Germany": 76,
                    "France": 65,
                    "Japan": 54,
                    "South Korea": 43,
                    "Canada": 38,
                    "Australia": 32
                }
            }
        };

        this.displayThreatStatistics(demoStats.stats);
        this.createVisualizationCharts(demoStats.stats);

        // Add demo notice to stats
        const statsContainer = document.getElementById('threat-statistics');
        const demoNotice = `
            <div class="alert alert-info mb-3">
                <i class="bi bi-info-circle"></i>
                <strong>Demo Mode:</strong> Displaying sample threat intelligence statistics.
                <button class="btn btn-outline-secondary btn-sm float-end" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
        `;
        statsContainer.innerHTML = demoNotice + statsContainer.innerHTML;
    }

    exportThreatActorData(format = 'json') {
        const resultsContainer = document.getElementById('cyfirma-results');
        const actorCards = resultsContainer.querySelectorAll('.threat-actor-card');

        if (actorCards.length === 0) {
            this.showAlert('No threat actor data to export. Please perform a search first.', 'warning');
            return;
        }

        // Extract data from the displayed cards
        const exportData = Array.from(actorCards).map(card => {
            const actorId = card.dataset.actorId;
            const name = card.querySelector('h6').textContent.trim().split('\n')[0].replace('🚫', '').trim();
            const severity = card.querySelector('.badge').textContent.trim();
            const confidence = card.querySelector('small').textContent.replace('Confidence: ', '').replace('%', '');
            const description = card.querySelector('.card-text').textContent.trim();

            return {
                stix_id: actorId,
                name: name,
                severity_level: severity.toLowerCase(),
                confidence_score: parseFloat(confidence) / 100,
                description: description,
                export_timestamp: new Date().toISOString()
            };
        });

        if (format === 'json') {
            this.downloadJSON(exportData, 'threat_actors.json');
        } else if (format === 'csv') {
            this.downloadCSV(exportData, 'threat_actors.csv');
        }
    }

    downloadJSON(data, filename) {
        const jsonStr = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showAlert(`Exported ${data.length} threat actors to ${filename}`, 'success');
    }

    downloadCSV(data, filename) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header => {
                    const value = row[header];
                    // Escape quotes and wrap in quotes if contains comma
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showAlert(`Exported ${data.length} threat actors to ${filename}`, 'success');
    }

    async performCyfirmaSearch() {
        const form = document.getElementById('cyfirma-search-form');
        const resultsContainer = document.getElementById('cyfirma-results');
        const submitButton = form.querySelector('button[type="submit"]');
        const actorNameInput = document.getElementById('cyfirma-actor-name');

        // Validate input
        const actorName = actorNameInput.value.trim();
        if (!actorName) {
            this.showAlert('Please enter a threat actor name to search.', 'warning');
            actorNameInput.focus();
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Searching...';
        resultsContainer.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Searching for "${actorName}" in Cyfirma database...</p>
            </div>
        `;

        try {
            // Simplified search parameters - only threat actor name
            const searchParams = {
                query: actorName,
                limit: 50,
                offset: 0
            };

            console.log('Searching for threat actor:', actorName);

            // Perform search
            const result = await this.searchCyfirmaActors(searchParams);

            if (result && result.actors && result.actors.length > 0) {
                this.displayCyfirmaActors(result);
                console.log(`Found ${result.actors.length} threat actor(s) matching "${actorName}"`);
            } else {
                resultsContainer.innerHTML = `
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle"></i>
                        <strong>No Results Found:</strong> No threat actors found matching "${actorName}".
                        <br><small>Try searching with different spelling, aliases, or partial names.</small>
                    </div>
                `;
            }

        } catch (error) {
            console.error('Cyfirma search error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Search Failed:</strong> ${error.message}
                    <br><small>Please check your connection and try again.</small>
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> Search Actor';
        }
    }

    async loadAllCyfirmaActors() {
        const resultsContainer = document.getElementById('cyfirma-results');

        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Loading all threat actors...</p></div>';

        try {
            const result = await this.searchCyfirmaActors({ limit: 100 });
            this.displayCyfirmaActors(result);
        } catch (error) {
            console.error('Failed to load all actors:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Load Failed:</strong> ${error.message}
                </div>
            `;
        }
    }

    clearActorSearch() {
        // Clear the simplified search input
        const actorNameInput = document.getElementById('cyfirma-actor-name');
        if (actorNameInput) {
            actorNameInput.value = '';
            actorNameInput.focus();
        }

        // Clear results and show initial state
        document.getElementById('cyfirma-results').innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-search display-4"></i>
                <p class="mt-2">Enter a threat actor name above to search the Cyfirma intelligence database.</p>
                <button class="btn btn-outline-primary" onclick="dashboard.loadAllCyfirmaActors()">
                    <i class="bi bi-download"></i> Load All Actors
                </button>
            </div>
        `;
    }

    // Keep the old function for backward compatibility
    clearCyfirmaFilters() {
        this.clearActorSearch();
    }

    // Helper function to show alerts
    showAlert(message, type = 'info') {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';

        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(alertContainer);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertContainer.parentNode) {
                alertContainer.remove();
            }
        }, 5000);
    }

    async viewActorDetails(actorName) {
        try {
            const result = await this.getCyfirmaActor(actorName);

            if (result.success) {
                this.showActorDetailsModal(result.actor, result.raw_stix);
            } else {
                this.showNotification(`Failed to load details for ${actorName}`, 'error');
            }
        } catch (error) {
            console.error('Failed to view actor details:', error);
            this.showNotification('Failed to load actor details', 'error');
        }
    }

    showActorDetailsModal(actor, rawStix) {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="actorDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-person-x"></i> ${actor.name}
                                <span class="badge bg-${this.getSeverityColor(actor.severity_level)} ms-2">
                                    ${actor.severity_level.toUpperCase()}
                                </span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>Description</h6>
                                    <p>${actor.description}</p>

                                    <h6>Attribution</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Origin Country:</strong> ${actor.origin_country || 'Unknown'}<br>
                                            <strong>Primary Motivation:</strong> ${actor.primary_motivation || 'Unknown'}<br>
                                            <strong>Actor Types:</strong> ${actor.actor_types.join(', ')}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Confidence Score:</strong> ${(actor.confidence_score * 100).toFixed(1)}%<br>
                                            <strong>Last Updated:</strong> ${new Date(actor.last_updated).toLocaleDateString()}<br>
                                            <strong>Source:</strong> ${actor.source}
                                        </div>
                                    </div>

                                    <h6 class="mt-3">Aliases</h6>
                                    <div class="mb-2">
                                        ${actor.aliases.map(alias => `<span class="badge bg-secondary me-1">${alias}</span>`).join('')}
                                    </div>

                                    <h6 class="mt-3">Target Profile</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Industries:</strong><br>
                                            ${actor.target_industries.slice(0, 10).map(industry => `<span class="badge bg-info me-1 mb-1">${industry}</span>`).join('')}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Countries:</strong><br>
                                            ${actor.target_countries.slice(0, 10).map(country => `<span class="badge bg-warning me-1 mb-1">${country}</span>`).join('')}
                                        </div>
                                    </div>

                                    <h6 class="mt-3">Technologies</h6>
                                    <div class="mb-2">
                                        ${actor.target_technologies.slice(0, 10).map(tech => `<span class="badge bg-success me-1 mb-1">${tech}</span>`).join('')}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>STIX Data</h6>
                                    <div class="bg-light p-2 rounded" style="max-height: 400px; overflow-y: auto;">
                                        <pre class="small">${JSON.stringify(rawStix, null, 2)}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-success" onclick="dashboard.addToWatchlist('${actor.name}', 'threat_actor')">
                                <i class="bi bi-plus-circle"></i> Add to Watchlist
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="dashboard.exportActorData('${actor.stix_id}')">
                                <i class="bi bi-download"></i> Export Data
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('actorDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM and show
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('actorDetailsModal'));
        modal.show();
    }

    async loadThreatStatistics() {
        const statsContainer = document.getElementById('threat-statistics');

        try {
            statsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Loading statistics...</p></div>';

            const result = await this.getCyfirmaStats();

            if (result.success) {
                this.displayThreatStatistics(result.stats);
                this.createVisualizationCharts(result.stats);
            } else {
                // Handle graceful fallback for service unavailable
                statsContainer.innerHTML = `
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-info-circle"></i>
                        <strong>Service Notice:</strong> ${result.error || 'Threat intelligence service is currently unavailable'}
                        <br><small class="text-muted">Statistics will be displayed when the service is restored. Please check back later.</small>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="bi bi-cloud-slash display-4 text-muted"></i>
                                    <h5 class="mt-3">Threat Actor Intelligence</h5>
                                    <p class="text-muted">Real-time threat actor statistics will appear here when the service is available.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }
        } catch (error) {
            console.error('Failed to load threat statistics:', error);
            statsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Connection Error:</strong> Unable to connect to threat intelligence service.
                    <br><small class="text-muted">Please check your internet connection and try again.</small>
                </div>
            `;
        }
    }

    displayThreatStatistics(stats) {
        const statsContainer = document.getElementById('threat-statistics');

        const statsHtml = `
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${stats.total_actors}</h3>
                            <p class="mb-0">Total Threat Actors</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${Object.keys(stats.origin_countries).length}</h3>
                            <p class="mb-0">Origin Countries</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>${Object.keys(stats.top_target_industries).length}</h3>
                            <p class="mb-0">Target Industries</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${new Date(stats.last_updated).toLocaleDateString()}</h3>
                            <p class="mb-0">Last Updated</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>Top Actor Types</h6>
                    <div class="list-group">
                        ${Object.entries(stats.actor_types).slice(0, 5).map(([type, count]) => `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                ${type.replace('-', ' ')}
                                <span class="badge bg-primary rounded-pill">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>Top Origin Countries</h6>
                    <div class="list-group">
                        ${Object.entries(stats.origin_countries).slice(0, 5).map(([country, count]) => `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                ${country}
                                <span class="badge bg-success rounded-pill">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        statsContainer.innerHTML = statsHtml;
    }

    createVisualizationCharts(stats) {
        // Create charts using the visualization module
        if (window.threatVisualization) {
            threatVisualization.createActorTypeChart('actorTypesChart', stats);
            threatVisualization.createGeographicChart('geographicChart', stats);
            threatVisualization.createSeverityChart('severityChart', stats);
            threatVisualization.createIndustriesChart('industriesChart', stats);

            // Load some actors for network visualization
            this.loadNetworkVisualization();
        }
    }

    async loadNetworkVisualization() {
        try {
            const result = await this.searchCyfirmaActors({ limit: 20 });
            if (result.actors && result.actors.length > 0) {
                threatVisualization.createNetworkVisualization('networkVisualization', result.actors);
            }
        } catch (error) {
            console.error('Failed to load network visualization:', error);
        }
    }

    async generateActorReport() {
        // Similar to analyzeThreatActor but calls the /actor/report endpoint
        this.showNotification('Report generation feature coming soon!', 'info');
    }

    // Passive Scanning
    async startPassiveScan() {
        const form = document.getElementById('passive-scan-form');
        const resultsContainer = document.getElementById('passive-scan-results');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Scanning...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Performing passive scan...</p></div>';

        try {
            const scanData = {
                target: document.getElementById('scan-target').value,
                scan_type: document.getElementById('scan-type').value
            };

            const response = await fetch(`${this.apiBaseUrl}/passive/scan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(scanData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayPassiveScanResults(result);
            } else {
                throw new Error(result.detail || 'Scan failed');
            }

        } catch (error) {
            console.error('Passive scan error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Scan Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> Start Passive Scan';
        }
    }

    displayPassiveScanResults(result) {
        const resultsContainer = document.getElementById('passive-scan-results');
        const scanResults = result.scan_results;

        const servicesHtml = scanResults.services.map(service => `
            <tr>
                <td>${service.port || 'N/A'}</td>
                <td>${service.service || 'Unknown'}</td>
                <td>${service.version || 'N/A'}</td>
                <td><span class="badge bg-info">${service.source || 'Unknown'}</span></td>
            </tr>
        `).join('');

        const vulnerabilitiesHtml = scanResults.vulnerabilities.map(vuln => `
            <div class="alert alert-warning" role="alert">
                <strong>${vuln.cve || 'Unknown CVE'}</strong><br>
                <small>${vuln.description || 'No description available'}</small>
            </div>
        `).join('');

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-search"></i> Scan Results for ${result.target}</h6>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">${scanResults.total_sources}</h4>
                            <small class="text-muted">Sources</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">${scanResults.services_found}</h4>
                            <small class="text-muted">Services</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">${scanResults.vulnerabilities_found}</h4>
                            <small class="text-muted">Vulnerabilities</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">${scanResults.open_ports.length}</h4>
                            <small class="text-muted">Open Ports</small>
                        </div>
                    </div>
                </div>

                ${scanResults.open_ports.length > 0 ? `
                    <div class="mb-3">
                        <strong>Open Ports:</strong>
                        <div class="mt-2">
                            ${scanResults.open_ports.map(port => `<span class="badge bg-secondary me-1">${port}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}

                ${scanResults.services.length > 0 ? `
                    <div class="mb-3">
                        <strong>Services:</strong>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Port</th>
                                        <th>Service</th>
                                        <th>Version</th>
                                        <th>Source</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${servicesHtml}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ` : ''}

                ${scanResults.vulnerabilities.length > 0 ? `
                    <div class="mb-3">
                        <strong>Vulnerabilities:</strong>
                        <div class="mt-2">
                            ${vulnerabilitiesHtml}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Watchlist Management
    async addWatchlistItem() {
        const form = document.getElementById('watchlist-form');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Adding...';

        try {
            const watchlistData = {
                value: document.getElementById('watchlist-value').value,
                item_type: document.getElementById('watchlist-type').value,
                description: document.getElementById('watchlist-description').value,
                severity: document.getElementById('watchlist-severity').value,
                tags: document.getElementById('watchlist-tags').value ?
                     document.getElementById('watchlist-tags').value.split(',').map(tag => tag.trim()) : [],
                expiry_days: document.getElementById('watchlist-expiry').value ?
                            parseInt(document.getElementById('watchlist-expiry').value) : null
            };

            const response = await fetch(`${this.apiBaseUrl}/watchlist/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(watchlistData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Item added to watchlist successfully!', 'success');
                form.reset();
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to add item');
            }

        } catch (error) {
            console.error('Watchlist add error:', error);
            this.showNotification(`Failed to add item: ${error.message}`, 'danger');
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-plus-circle"></i> Add to Watchlist';
        }
    }

    async loadWatchlistData() {
        try {
            // Load watchlist items
            const itemsResponse = await fetch(`${this.apiBaseUrl}/watchlist/items`);
            if (itemsResponse.ok) {
                const itemsData = await itemsResponse.json();
                if (itemsData.success) {
                    this.displayWatchlistItems(itemsData.items);
                }
            }

            // Load alerts
            const alertsResponse = await fetch(`${this.apiBaseUrl}/watchlist/alerts?limit=10`);
            if (alertsResponse.ok) {
                const alertsData = await alertsResponse.json();
                if (alertsData.success) {
                    this.displayWatchlistAlerts(alertsData.alerts);
                }
            }

        } catch (error) {
            console.error('Error loading watchlist data:', error);
        }
    }

    displayWatchlistItems(items) {
        const container = document.getElementById('watchlist-items');

        if (items.length === 0) {
            container.innerHTML = '<p class="text-muted">No watchlist items found.</p>';
            return;
        }

        const itemsHtml = items.map(item => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title">${item.value}</h6>
                            <p class="card-text text-muted small">${item.description}</p>
                            <div>
                                <span class="badge bg-${this.getSeverityClass(item.severity)}">${item.severity}</span>
                                <span class="badge bg-secondary">${item.type}</span>
                                ${item.tags.map(tag => `<span class="badge bg-light text-dark">${tag}</span>`).join(' ')}
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">Matches: ${item.match_count}</small><br>
                            <button class="btn btn-sm btn-outline-danger" onclick="dashboard.removeWatchlistItem('${item.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = itemsHtml;
    }

    displayWatchlistAlerts(alerts) {
        const container = document.getElementById('watchlist-alerts');

        if (alerts.length === 0) {
            container.innerHTML = '<p class="text-muted">No recent alerts.</p>';
            return;
        }

        const alertsHtml = alerts.map(alert => `
            <div class="alert alert-${this.getSeverityClass(alert.severity)} alert-dismissible fade show" role="alert">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${alert.matched_value}</strong><br>
                        <small>Match type: ${alert.match_type} | Source: ${alert.source}</small><br>
                        <small class="text-muted">${new Date(alert.timestamp).toLocaleString()}</small>
                    </div>
                    <div>
                        ${!alert.acknowledged ? `
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.acknowledgeAlert('${alert.id}')">
                                Acknowledge
                            </button>
                        ` : '<small class="text-muted">Acknowledged</small>'}
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = alertsHtml;
    }

    async removeWatchlistItem(itemId) {
        if (!confirm('Are you sure you want to remove this item from the watchlist?')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/watchlist/${itemId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Item removed from watchlist', 'success');
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to remove item');
            }

        } catch (error) {
            console.error('Remove watchlist item error:', error);
            this.showNotification(`Failed to remove item: ${error.message}`, 'danger');
        }
    }

    async acknowledgeAlert(alertId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/watchlist/alerts/${alertId}/acknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    acknowledged_by: 'user',
                    notes: 'Acknowledged via web interface'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Alert acknowledged', 'success');
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to acknowledge alert');
            }

        } catch (error) {
            console.error('Acknowledge alert error:', error);
            this.showNotification(`Failed to acknowledge alert: ${error.message}`, 'danger');
        }
    }

    refreshWatchlist() {
        this.loadWatchlistData();
        this.showNotification('Watchlist refreshed', 'info');
    }

    // API Testing
    async testApiConnections() {
        const testButton = document.querySelector('button[onclick="dashboard.testApiConnections()"]');
        testButton.disabled = true;
        testButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Testing...';

        const results = {};

        // Test backend connection first
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            results.backend = response.ok ? 'success' : 'warning';
        } catch (error) {
            results.backend = 'danger';
        }

        // Test API keys by checking if they're configured
        results.gemini = this.apiKeys.gemini ? 'info' : 'secondary';
        results.deepseek = this.apiKeys.deepseek ? 'info' : 'secondary';
        results.abuseipdb = this.apiKeys.abuseipdb ? 'info' : 'secondary';
        results.virustotal = this.apiKeys.virustotal ? 'info' : 'secondary';
        results.shodan = this.apiKeys.shodan ? 'info' : 'secondary';
        results.censys = (this.apiKeys.censys_id && this.apiKeys.censys_secret) ? 'info' : 'secondary';
        results.zoomeye = this.apiKeys.zoomeye ? 'info' : 'secondary';

        // Display results
        const resultsHtml = `
            <div class="mt-4">
                <h6>Connection Test Results</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Backend API
                                <span class="badge bg-${results.backend}">
                                    ${results.backend === 'success' ? 'Connected' : results.backend === 'warning' ? 'Issues' : 'Failed'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Gemini API
                                <span class="badge bg-${results.gemini}">
                                    ${results.gemini === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                DeepSeek API
                                <span class="badge bg-${results.deepseek}">
                                    ${results.deepseek === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                AbuseIPDB API
                                <span class="badge bg-${results.abuseipdb}">
                                    ${results.abuseipdb === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                VirusTotal API
                                <span class="badge bg-${results.virustotal}">
                                    ${results.virustotal === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Shodan API
                                <span class="badge bg-${results.shodan}">
                                    ${results.shodan === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Censys API
                                <span class="badge bg-${results.censys}">
                                    ${results.censys === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ZoomEye API
                                <span class="badge bg-${results.zoomeye}">
                                    ${results.zoomeye === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        `;

        // Add results to settings section
        const settingsSection = document.getElementById('settings-section');
        const existingResults = settingsSection.querySelector('.test-results');
        if (existingResults) {
            existingResults.remove();
        }

        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'test-results';
        resultsDiv.innerHTML = resultsHtml;
        settingsSection.appendChild(resultsDiv);

        // Reset button
        testButton.disabled = false;
        testButton.innerHTML = '<i class="bi bi-wifi"></i> Test Connections';

        this.showNotification('Connection test completed', 'info');
    }

    // Utility Methods
    async makeApiRequest(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const requestOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, requestOptions);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Unable to connect to backend. Please check if the server is running.');
            }
            throw error;
        }
    }

    // Enhanced error handling
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);

        let userMessage = error.message;

        // Provide user-friendly error messages
        if (error.message.includes('Unable to connect')) {
            userMessage = 'Cannot connect to the backend server. Please ensure it is running on http://127.0.0.1:8000';
        } else if (error.message.includes('CORS')) {
            userMessage = 'Cross-origin request blocked. Please check CORS configuration on the backend.';
        } else if (error.message.includes('401')) {
            userMessage = 'Authentication required. Please check your API keys.';
        } else if (error.message.includes('403')) {
            userMessage = 'Access forbidden. Please verify your permissions.';
        } else if (error.message.includes('429')) {
            userMessage = 'Rate limit exceeded. Please wait before making more requests.';
        } else if (error.message.includes('500')) {
            userMessage = 'Server error occurred. Please try again later.';
        }

        this.showNotification(userMessage, 'danger');
        return userMessage;
    }

    // Auto-retry mechanism for failed requests
    async retryApiRequest(requestFn, maxRetries = 3, delay = 1000) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }

                console.warn(`Request attempt ${attempt} failed, retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2; // Exponential backoff
            }
        }
    }

    // Batch operations
    async batchIngestIoCs(iocs) {
        try {
            const response = await this.makeApiRequest('/ioc/batch', {
                method: 'POST',
                body: JSON.stringify({ iocs })
            });

            if (response.success) {
                this.showNotification(`Successfully processed ${response.processed} IoCs`, 'success');
                return response;
            } else {
                throw new Error('Batch ingestion failed');
            }
        } catch (error) {
            this.handleApiError(error, 'batch IoC ingestion');
            throw error;
        }
    }

    // System configuration management
    async updateSystemConfig(config) {
        try {
            const response = await this.makeApiRequest('/system/config', {
                method: 'POST',
                body: JSON.stringify(config)
            });

            if (response.success) {
                this.showNotification('System configuration updated', 'success');
                return response;
            } else {
                throw new Error('Configuration update failed');
            }
        } catch (error) {
            this.handleApiError(error, 'system configuration update');
            throw error;
        }
    }

    async getSystemConfig() {
        try {
            const response = await this.makeApiRequest('/system/config');
            return response.config;
        } catch (error) {
            this.handleApiError(error, 'getting system configuration');
            return {};
        }
    }

    // Enhanced loading states
    setLoadingState(elementId, isLoading, loadingText = 'Loading...') {
        const element = document.getElementById(elementId);
        if (!element) return;

        if (isLoading) {
            element.classList.add('loading');
            const originalContent = element.innerHTML;
            element.dataset.originalContent = originalContent;
            element.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">${loadingText}</span>
                </div>
            `;
        } else {
            element.classList.remove('loading');
            if (element.dataset.originalContent) {
                element.innerHTML = element.dataset.originalContent;
                delete element.dataset.originalContent;
            }
        }
    }

    // Data export functionality
    exportData(data, filename, format = 'json') {
        let content, mimeType;

        switch (format.toLowerCase()) {
            case 'json':
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
                break;
            case 'csv':
                content = this.convertToCSV(data);
                mimeType = 'text/csv';
                break;
            default:
                throw new Error('Unsupported export format');
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification(`Data exported as ${filename}.${format}`, 'success');
    }

    convertToCSV(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return '';
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header => {
                    const value = row[header];
                    // Escape commas and quotes in CSV
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        return csvContent;
    }

    // Real-time Features
    startRealTimeUpdates() {
        // Update dashboard every 30 seconds
        this.dashboardUpdateInterval = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);

        // Update watchlist alerts every 15 seconds
        this.alertsUpdateInterval = setInterval(() => {
            if (this.currentSection === 'watchlist' || this.currentSection === 'dashboard') {
                this.updateAlertsRealTime();
            }
        }, 15000);

        // Update system status every 60 seconds
        this.statusUpdateInterval = setInterval(() => {
            this.checkBackendConnection();
        }, 60000);

        console.log('Real-time updates started');
    }

    stopRealTimeUpdates() {
        if (this.dashboardUpdateInterval) {
            clearInterval(this.dashboardUpdateInterval);
        }
        if (this.alertsUpdateInterval) {
            clearInterval(this.alertsUpdateInterval);
        }
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
        console.log('Real-time updates stopped');
    }

    async updateAlertsRealTime() {
        try {
            const response = await this.makeApiRequest('/watchlist/alerts?acknowledged=false&limit=10');

            if (response.success) {
                // Update alert count
                const alertCount = response.total_alerts || 0;
                const alertCountElement = document.getElementById('alerts-count');
                if (alertCountElement) {
                    const currentCount = parseInt(alertCountElement.textContent) || 0;
                    if (alertCount !== currentCount) {
                        alertCountElement.textContent = alertCount;

                        // Animate the change
                        alertCountElement.classList.add('text-warning');
                        setTimeout(() => {
                            alertCountElement.classList.remove('text-warning');
                        }, 2000);

                        // Show notification for new alerts
                        if (alertCount > currentCount) {
                            this.showNotification(`${alertCount - currentCount} new alert(s) detected!`, 'warning');
                            this.playNotificationSound();
                        }
                    }
                }

                // Update alerts display if on dashboard
                if (this.currentSection === 'dashboard') {
                    this.displayLatestAlerts(response.alerts || []);
                }

                // Update watchlist alerts if on watchlist page
                if (this.currentSection === 'watchlist') {
                    this.displayWatchlistAlerts(response.alerts || []);
                }
            }
        } catch (error) {
            console.warn('Failed to update alerts in real-time:', error);
        }
    }

    playNotificationSound() {
        // Create a simple notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }

    // Live Activity Feed
    addActivityItem(activity) {
        const activityContainer = document.getElementById('recent-activity');
        if (!activityContainer) return;

        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item fade-in mb-2';
        activityItem.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="activity-icon me-3">
                    <i class="bi bi-${activity.icon} text-${activity.type}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="activity-content">
                        <strong>${activity.title}</strong>
                        <p class="mb-1 text-muted">${activity.description}</p>
                        <small class="text-muted">${this.formatTimeAgo(activity.timestamp)}</small>
                    </div>
                </div>
            </div>
        `;

        // Add to top of activity feed
        activityContainer.insertBefore(activityItem, activityContainer.firstChild);

        // Remove old items (keep only last 10)
        const items = activityContainer.querySelectorAll('.activity-item');
        if (items.length > 10) {
            items[items.length - 1].remove();
        }
    }

    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    }

    // WebSocket-like functionality simulation
    simulateWebSocketUpdates() {
        // Simulate receiving updates from the backend
        const updateTypes = [
            {
                type: 'ioc_analyzed',
                icon: 'bug',
                title: 'IoC Analysis Complete',
                description: 'New IP address analyzed with high confidence',
                type: 'success'
            },
            {
                type: 'alert_triggered',
                icon: 'exclamation-triangle',
                title: 'Watchlist Alert',
                description: 'Suspicious domain detected in network traffic',
                type: 'warning'
            },
            {
                type: 'scan_completed',
                icon: 'search',
                title: 'Passive Scan Complete',
                description: 'Target scan finished, 5 services discovered',
                type: 'info'
            },
            {
                type: 'actor_updated',
                icon: 'person-x',
                title: 'Threat Actor Profile Updated',
                description: 'New TTPs added to existing actor profile',
                type: 'primary'
            }
        ];

        // Simulate random updates every 30-120 seconds
        const scheduleNextUpdate = () => {
            const delay = Math.random() * 90000 + 30000; // 30-120 seconds
            setTimeout(() => {
                const update = updateTypes[Math.floor(Math.random() * updateTypes.length)];
                update.timestamp = new Date().toISOString();
                this.addActivityItem(update);
                scheduleNextUpdate();
            }, delay);
        };

        scheduleNextUpdate();
    }

    // Enhanced notification system
    showEnhancedNotification(title, message, type = 'info', actions = []) {
        const notificationId = 'notification-' + Date.now();
        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 350px; max-width: 400px;';

        const actionsHtml = actions.map(action =>
            `<button type="button" class="btn btn-sm btn-outline-${type} me-2" onclick="${action.onclick}">${action.text}</button>`
        ).join('');

        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="me-3">
                    <i class="bi bi-${this.getNotificationIcon(type)} fs-4"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">${title}</h6>
                    <p class="mb-2">${message}</p>
                    ${actionsHtml ? `<div class="mb-2">${actionsHtml}</div>` : ''}
                    <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds if no actions
        if (actions.length === 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        return notificationId;
    }

    getNotificationIcon(type) {
        const iconMap = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'x-circle',
            'info': 'info-circle',
            'primary': 'bell'
        };
        return iconMap[type] || 'bell';
    }

    // Enhanced Theme Management System
    loadThemePreference() {
        const savedTheme = localStorage.getItem('cti-dashboard-theme');
        // Check if saved theme exists and is valid
        if (savedTheme && this.themes && this.themes[savedTheme]) {
            return savedTheme;
        }
        return 'light-professional';
    }

    saveThemePreference(theme) {
        localStorage.setItem('cti-dashboard-theme', theme);
    }

    initializeTheme() {
        this.applyTheme(this.currentTheme);
        this.updateThemeToggleIcon();
    }

    applyTheme(themeName) {
        console.log('Applying theme:', themeName); // Debug log

        if (!this.themes[themeName]) {
            console.warn(`Theme ${themeName} not found, falling back to light-professional`);
            themeName = 'light-professional';
        }

        const themeLink = document.getElementById('theme-css');
        if (!themeLink) {
            console.error('Theme CSS link element not found!');
            return;
        }

        const theme = this.themes[themeName];
        console.log('Theme config:', theme); // Debug log

        // Add transition class for smooth theme switching
        document.body.classList.add('theme-transitioning');

        // Apply the theme
        console.log('Setting theme CSS href to:', theme.file); // Debug log
        themeLink.href = theme.file;

        // Clear existing theme classes and apply new one
        document.body.className = document.body.className.replace(/theme-\w+-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`, 'theme-transitioning');

        console.log('Applied body classes:', document.body.className); // Debug log
        console.log('Theme CSS link href is now:', themeLink.href); // Debug log

        // Remove transition class after animation
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);

        this.currentTheme = themeName;
        this.saveThemePreference(themeName);
        this.updateThemeToggleIcon();
    }

    toggleTheme() {
        console.log('Toggle theme clicked, current theme:', this.currentTheme); // Debug log
        const newTheme = this.currentTheme === 'light-professional' ? 'dark-professional' : 'light-professional';
        console.log('Switching to theme:', newTheme); // Debug log
        this.applyTheme(newTheme);

        // Add a subtle animation to the toggle button
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.style.transform = 'scale(0.95)';
            setTimeout(() => {
                toggleButton.style.transform = '';
            }, 150);
        }
    }

    updateThemeToggleIcon() {
        const themeIcon = document.getElementById('theme-icon');
        const toggleButton = document.getElementById('theme-toggle');

        if (themeIcon && toggleButton) {
            const theme = this.themes[this.currentTheme];
            themeIcon.className = `bi ${theme.icon}`;
            toggleButton.title = `Switch to ${this.currentTheme === 'light-professional' ? 'Dark' : 'Light'} Theme`;
        }
    }



    // Visual Enhancement Functions
    animateCounter(elementId, targetValue, duration = 1000) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - startValue) / (duration / 16);
        let currentValue = startValue;

        element.classList.add('counting');

        const timer = setInterval(() => {
            currentValue += increment;
            if ((increment > 0 && currentValue >= targetValue) ||
                (increment < 0 && currentValue <= targetValue)) {
                currentValue = targetValue;
                clearInterval(timer);
                element.classList.remove('counting');
            }
            element.textContent = Math.floor(currentValue);
        }, 16);
    }

    updateProgressRing(elementId, percentage) {
        const circle = document.getElementById(elementId);
        if (!circle) return;

        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        const offset = circumference - (percentage / 100) * circumference;

        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = offset;
    }

    createHeatmap(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.className = 'heatmap-grid';

        data.forEach((value, index) => {
            const cell = document.createElement('div');
            cell.className = `heatmap-cell intensity-${Math.min(4, Math.floor(value * 5))}`;
            cell.title = `Value: ${value}`;
            cell.addEventListener('click', () => {
                this.showNotification(`Heatmap cell ${index}: ${value}`, 'info');
            });
            container.appendChild(cell);
        });
    }

    createTimelineChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.className = 'timeline-chart';

        const maxValue = Math.max(...data);

        data.forEach((value, index) => {
            const bar = document.createElement('div');
            bar.className = 'timeline-bar';
            if (value > maxValue * 0.8) {
                bar.classList.add('high-activity');
            }

            const height = (value / maxValue) * 180;
            bar.style.height = `${height}px`;
            bar.style.left = `${(index / data.length) * 100}%`;
            bar.title = `Time ${index}: ${value} events`;

            container.appendChild(bar);
        });
    }

    createNetworkTopology(containerId, nodes, connections) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.style.position = 'relative';
        container.style.height = '300px';

        // Create nodes
        nodes.forEach((node, index) => {
            const nodeElement = document.createElement('div');
            nodeElement.className = `network-node ${node.status || ''}`;
            nodeElement.textContent = node.label;
            nodeElement.style.position = 'absolute';
            nodeElement.style.left = `${node.x}%`;
            nodeElement.style.top = `${node.y}%`;
            nodeElement.title = `${node.label}: ${node.status || 'normal'}`;

            nodeElement.addEventListener('click', () => {
                this.showNotification(`Node: ${node.label} (${node.status || 'normal'})`, 'info');
            });

            container.appendChild(nodeElement);
        });

        // Create connections
        connections.forEach(connection => {
            const line = document.createElement('div');
            line.className = 'network-connection';

            const fromNode = nodes[connection.from];
            const toNode = nodes[connection.to];

            const deltaX = toNode.x - fromNode.x;
            const deltaY = toNode.y - fromNode.y;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;

            line.style.width = `${distance}%`;
            line.style.left = `${fromNode.x}%`;
            line.style.top = `${fromNode.y}%`;
            line.style.transform = `rotate(${angle}deg)`;

            container.appendChild(line);
        });
    }



    // Enhanced dashboard data loading with visual updates
    async loadDashboardDataEnhanced() {
        try {
            // Load regular dashboard data
            await this.loadDashboardData();

            // Add visual enhancements
            const watchlistCount = parseInt(document.getElementById('watchlist-count').textContent) || 0;
            const alertsCount = parseInt(document.getElementById('alerts-count').textContent) || 0;

            // Animate counters
            this.animateCounter('watchlist-count', watchlistCount);
            this.animateCounter('alerts-count', alertsCount);

            // Update progress ring for watchlist
            this.updateProgressRing('watchlist-progress', Math.min(100, (watchlistCount / 50) * 100));

            // Create sample heatmap data
            const heatmapData = Array.from({length: 100}, () => Math.random());

            // Create sample timeline data
            const timelineData = Array.from({length: 24}, () => Math.floor(Math.random() * 100));

            // Update threat level based on alerts
            const threatLevelElement = document.getElementById('alert-threat-level');
            if (threatLevelElement) {
                let level = 'low';
                let levelText = 'Low Risk';

                if (alertsCount > 10) {
                    level = 'high';
                    levelText = 'High Risk';
                } else if (alertsCount > 5) {
                    level = 'medium';
                    levelText = 'Medium Risk';
                }

                threatLevelElement.className = `threat-level ${level}`;
                threatLevelElement.innerHTML = `<span class="status-dot ${level === 'low' ? 'online' : level === 'medium' ? 'warning' : 'offline'}"></span>${levelText}`;
            }

        } catch (error) {
            console.error('Error loading enhanced dashboard data:', error);
        }
    }
}

// Check if CTIDashboard class is defined
console.log('CTIDashboard class defined:', typeof CTIDashboard);

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, creating CTI Dashboard...'); // Debug log
    console.log('CTIDashboard class available:', typeof CTIDashboard);

    try {
        window.dashboard = new CTIDashboard();
        console.log('Dashboard created successfully:', window.dashboard); // Debug log
        console.log('Dashboard toggleTheme method exists:', typeof window.dashboard.toggleTheme); // Debug log

        // List all methods available on the dashboard instance
        console.log('Available methods on dashboard:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.dashboard)));

        // Check if toggleTheme exists in the prototype
        console.log('toggleTheme in prototype:', 'toggleTheme' in Object.getPrototypeOf(window.dashboard));

    } catch (error) {
        console.error('Error creating dashboard:', error);
        console.error('Error stack:', error.stack);
    }

    // Add global test function for debugging
    window.testThemeToggle = function() {
        console.log('Manual theme toggle test');
        if (window.dashboard && typeof window.dashboard.toggleTheme === 'function') {
            window.dashboard.toggleTheme();
        } else {
            console.error('Dashboard not initialized or toggleTheme method missing');
            console.log('Dashboard object:', window.dashboard);
        }
    };


});
