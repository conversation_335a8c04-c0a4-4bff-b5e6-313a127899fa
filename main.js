/**
 * CTI Dashboard Frontend - Main Entry Point
 * This is the single main entry point for the CTI Dashboard frontend application.
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://127.0.0.1:8000',
    FRONTEND_PORT: 8080,
    AUTO_REFRESH_INTERVAL: 30000, // 30 seconds
    THEME_STORAGE_KEY: 'cti-dashboard-theme',
    API_KEYS_STORAGE_KEY: 'cti-dashboard-api-keys'
};

// Global application state
let dashboard = null;

/**
 * Initialize the CTI Dashboard application
 */
function initializeDashboard() {
    console.log('🛡️ CTI Dashboard Frontend Initializing...');
    console.log('📡 API Base URL:', CONFIG.API_BASE_URL);
    
    // Check if we're in the new structure
    const isNewStructure = window.location.pathname.includes('/src/frontend/');
    
    if (isNewStructure) {
        console.log('✅ Running in new project structure');
    } else {
        console.log('⚠️  Running in legacy structure');
    }
    
    // Initialize the dashboard application
    try {
        // Check if CTIDashboard class is available
        if (typeof CTIDashboard !== 'undefined') {
            dashboard = new CTIDashboard();
            console.log('✅ CTI Dashboard initialized successfully');
        } else {
            console.error('❌ CTIDashboard class not found. Make sure app.js is loaded.');
            showInitializationError('CTIDashboard class not found');
        }
    } catch (error) {
        console.error('❌ Failed to initialize CTI Dashboard:', error);
        showInitializationError(error.message);
    }
}

/**
 * Show initialization error to user
 */
function showInitializationError(message) {
    const errorHtml = `
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">⚠️ Initialization Error</h4>
            <p>Failed to initialize the CTI Dashboard application:</p>
            <hr>
            <p class="mb-0"><strong>Error:</strong> ${message}</p>
            <p class="mb-0"><strong>Solution:</strong> Please refresh the page or check the browser console for more details.</p>
        </div>
    `;
    
    // Try to show error in main content area
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.innerHTML = errorHtml;
    } else {
        // Fallback: create error display
        document.body.innerHTML = `
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        ${errorHtml}
                    </div>
                </div>
            </div>
        `;
    }
}

/**
 * Check if all required dependencies are loaded
 */
function checkDependencies() {
    const requiredGlobals = [
        'bootstrap', // Bootstrap JS
        'Chart'      // Chart.js
    ];
    
    const missing = requiredGlobals.filter(dep => typeof window[dep] === 'undefined');
    
    if (missing.length > 0) {
        console.warn('⚠️  Missing dependencies:', missing);
        return false;
    }
    
    return true;
}

/**
 * Wait for dependencies to load with timeout
 */
function waitForDependencies(timeout = 10000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        function check() {
            if (checkDependencies()) {
                resolve();
            } else if (Date.now() - startTime > timeout) {
                reject(new Error('Timeout waiting for dependencies'));
            } else {
                setTimeout(check, 100);
            }
        }
        
        check();
    });
}

/**
 * Main application startup
 */
async function startApplication() {
    try {
        console.log('🚀 Starting CTI Dashboard Application...');
        
        // Wait for dependencies
        await waitForDependencies();
        console.log('✅ All dependencies loaded');
        
        // Initialize the dashboard
        initializeDashboard();
        
    } catch (error) {
        console.error('❌ Failed to start application:', error);
        showInitializationError(error.message);
    }
}

// Application entry point
document.addEventListener('DOMContentLoaded', startApplication);

// Export for global access
window.CTI_MAIN = {
    CONFIG,
    initializeDashboard,
    startApplication,
    getDashboard: () => dashboard
};

console.log('📝 CTI Dashboard Main Entry Point Loaded');
