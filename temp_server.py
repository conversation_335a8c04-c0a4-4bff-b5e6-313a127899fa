import http.server 
import socketserver 
import webbrowser 
import os 
import sys 
from pathlib import Path 
 
PORT = 8080 
Handler = http.server.SimpleHTTPRequestHandler 
 
try: 
    with socketserver.TCPServer(("", PORT), Handler) as httpd: 
        print(f"🌐 Server running at: http://localhost:{PORT}") 
        print(f"📚 Main dashboard: http://localhost:{PORT}/src/frontend/index.html") 
        print("🔧 Press Ctrl+C to stop") 
        try: 
            webbrowser.open(f"http://localhost:{PORT}/src/frontend/index.html") 
        except: 
            pass 
        httpd.serve_forever() 
except KeyboardInterrupt: 
    print("\n👋 Server stopped") 
except Exception as e: 
    print(f"❌ Error: {e}") 
