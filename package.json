{"name": "cti-dashboard", "version": "1.0.0", "description": "Cyber Threat Intelligence Dashboard - Comprehensive threat analysis and monitoring platform", "main": "main.js", "scripts": {"start": "node -e \"console.log('Use start-all.bat to start both frontend and backend')\"", "start:frontend": "node -e \"console.log('Use start-frontend.bat to start the frontend server')\"", "start:backend": "node -e \"console.log('Use start-backend.bat to start the backend server')\"", "dev": "node -e \"console.log('Use start-all.bat for development mode')\"", "test": "echo \"No tests specified yet\"", "lint": "echo \"No linting configured yet\""}, "repository": {"type": "git", "url": "."}, "keywords": ["cybersecurity", "threat-intelligence", "cti", "dashboard", "security-analysis", "ioc", "threat-actors", "passive-scanning", "watchlist"], "author": "CTI Dashboard Team", "license": "MIT", "engines": {"node": ">=14.0.0", "python": ">=3.8.0"}, "devDependencies": {}, "dependencies": {}, "directories": {"src": "src", "frontend": "src/frontend", "backend": "src/backend", "shared": "src/shared"}, "files": ["src/", "main.js", "server.py", "requirements.txt", "start-*.bat", "README.md"]}