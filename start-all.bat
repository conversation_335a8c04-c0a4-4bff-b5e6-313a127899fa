@echo off
echo 🛡️ CTI Dashboard - Complete System Startup
echo =============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "server.py" (
    echo ❌ server.py not found!
    echo Make sure you're running this from the project root
    pause
    exit /b 1
)

if not exist "src\backend" (
    echo ❌ Backend directory not found!
    pause
    exit /b 1
)

if not exist "src\frontend" (
    echo ❌ Frontend directory not found!
    pause
    exit /b 1
)

echo 🚀 Starting CTI Dashboard Complete System...
echo.
echo This will start both:
echo   🔧 Backend API Server (Port 8000)
echo   🌐 Frontend Web Server (Port 8080)
echo.
echo ⚠️  Make sure ports 8000 and 8080 are available
echo.

REM Install backend dependencies if needed
if exist "src\backend\requirements.txt" (
    echo 📦 Installing backend dependencies...
    pip install -r src\backend\requirements.txt >nul 2>&1
)

echo 🔧 Starting Backend Server...
start "CTI Dashboard Backend" cmd /k "echo 🛡️ CTI Dashboard Backend Server && echo ===================================== && echo. && python server.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo 🌐 Starting Frontend Server...
start "CTI Dashboard Frontend" cmd /k "start-frontend.bat"

echo.
echo ✅ Both servers are starting in separate windows:
echo.
echo 🔧 Backend Server:
echo    📚 API Documentation: http://localhost:8000/docs
echo    🔍 Health Check: http://localhost:8000/health
echo.
echo 🌐 Frontend Server:
echo    📱 Main Dashboard: http://localhost:8080/src/frontend/index.html
echo    🎨 Component Demo: http://localhost:8080/src/frontend/demo.html
echo.
echo 💡 Tips:
echo    - Close the server windows to stop the services
echo    - Check the individual windows for any error messages
echo    - Make sure to configure API keys for full functionality
echo.

pause
