<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test - CTI Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/visual-enhancements.css" rel="stylesheet">
    <!-- Theme CSS -->
    <link id="theme-css" href="css/themes/light-professional.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h1>Theme Test</h1>
                <button class="btn btn-primary" id="theme-toggle">
                    <i class="bi bi-moon-fill" id="theme-icon"></i>
                    Toggle Theme
                </button>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Card</h5>
                    </div>
                    <div class="card-body">
                        <p>This is a test card to verify theme switching.</p>
                        <div class="progress mb-3">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                        <span class="badge bg-primary">Primary</span>
                        <span class="badge bg-success">Success</span>
                        <span class="badge bg-warning">Warning</span>
                        <span class="badge bg-danger">Danger</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Form Test</h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label class="form-label">Test Input</label>
                                <input type="text" class="form-control" placeholder="Enter text">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Test Select</label>
                                <select class="form-select">
                                    <option>Option 1</option>
                                    <option>Option 2</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-success">Test Button</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Visual Elements</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress-ring mb-3">
                            <svg>
                                <circle class="bg-circle" cx="60" cy="60" r="45"></circle>
                                <circle class="progress-circle" cx="60" cy="60" r="45" style="stroke-dasharray: 283 283; stroke-dashoffset: 113;"></circle>
                            </svg>
                            <div class="progress-text">75%</div>
                        </div>
                        <div class="animated-counter">1,234</div>
                        <small class="text-muted d-block">Animated Counter</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Data Visualization Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Heatmap</h6>
                                <div id="test-heatmap"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Timeline Chart</h6>
                                <div id="test-timeline"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple theme management for testing
        const themes = {
            'light-professional': {
                name: 'Light Professional',
                file: 'css/themes/light-professional.css',
                icon: 'bi-moon-fill'
            },
            'dark-professional': {
                name: 'Dark Professional', 
                file: 'css/themes/dark-professional.css',
                icon: 'bi-sun-fill'
            }
        };
        
        let currentTheme = 'light-professional';
        
        function toggleTheme() {
            console.log('Toggle theme clicked, current theme:', currentTheme);
            const newTheme = currentTheme === 'light-professional' ? 'dark-professional' : 'light-professional';
            console.log('Switching to theme:', newTheme);
            
            const themeLink = document.getElementById('theme-css');
            const theme = themes[newTheme];
            
            if (themeLink && theme) {
                console.log('Setting theme CSS href to:', theme.file);
                themeLink.href = theme.file;
                
                // Update icon
                const themeIcon = document.getElementById('theme-icon');
                if (themeIcon) {
                    themeIcon.className = `bi ${theme.icon}`;
                }
                
                currentTheme = newTheme;
                console.log('Theme switched to:', currentTheme);
                console.log('Theme CSS link href is now:', themeLink.href);
            } else {
                console.error('Theme link or theme config not found');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
                console.log('Theme toggle initialized');
            }
            
            // Create test visualizations
            createTestHeatmap();
            createTestTimeline();
        });
        
        function createTestHeatmap() {
            const container = document.getElementById('test-heatmap');
            if (!container) return;
            
            container.innerHTML = '';
            container.className = 'heatmap-grid';
            
            for (let i = 0; i < 25; i++) {
                const cell = document.createElement('div');
                const intensity = Math.floor(Math.random() * 5);
                cell.className = `heatmap-cell intensity-${intensity}`;
                cell.title = `Cell ${i}: Intensity ${intensity}`;
                container.appendChild(cell);
            }
        }
        
        function createTestTimeline() {
            const container = document.getElementById('test-timeline');
            if (!container) return;
            
            container.innerHTML = '';
            container.className = 'timeline-chart';
            
            for (let i = 0; i < 12; i++) {
                const bar = document.createElement('div');
                bar.className = 'timeline-bar';
                const height = Math.random() * 150 + 20;
                bar.style.height = `${height}px`;
                bar.style.left = `${(i / 12) * 90 + 5}%`;
                bar.style.width = '6%';
                bar.title = `Hour ${i}: ${Math.floor(height)} events`;
                container.appendChild(bar);
            }
        }
    </script>
</body>
</html>
