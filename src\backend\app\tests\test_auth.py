"""
Authentication tests
"""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from ..models.user import User, UserCreate, UserLogin
from ..services.auth_service import AuthService
from ..core.exceptions import AuthenticationError


class TestAuthService:
    """Test authentication service"""
    
    def test_create_user(self, db_session: Session):
        """Test user creation"""
        auth_service = AuthService(db_session)
        
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123",
            full_name="Test User"
        )
        
        user = auth_service.create_user(user_data)
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.full_name == "Test User"
        assert user.hashed_password != "TestPassword123"  # Should be hashed
        assert user.is_active is True
    
    def test_create_duplicate_user(self, db_session: Session):
        """Test creating duplicate user fails"""
        auth_service = AuthService(db_session)
        
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        
        # Create first user
        auth_service.create_user(user_data)
        
        # Try to create duplicate
        with pytest.raises(AuthenticationError, match="Username already exists"):
            auth_service.create_user(user_data)
    
    def test_authenticate_user_success(self, db_session: Session):
        """Test successful user authentication"""
        auth_service = AuthService(db_session)
        
        # Create user
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        created_user = auth_service.create_user(user_data)
        
        # Authenticate
        authenticated_user = auth_service.authenticate_user("testuser", "TestPassword123")
        
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        assert authenticated_user.username == "testuser"
    
    def test_authenticate_user_wrong_password(self, db_session: Session):
        """Test authentication with wrong password"""
        auth_service = AuthService(db_session)
        
        # Create user
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        auth_service.create_user(user_data)
        
        # Try to authenticate with wrong password
        authenticated_user = auth_service.authenticate_user("testuser", "WrongPassword")
        
        assert authenticated_user is None
    
    def test_authenticate_nonexistent_user(self, db_session: Session):
        """Test authentication with nonexistent user"""
        auth_service = AuthService(db_session)
        
        authenticated_user = auth_service.authenticate_user("nonexistent", "password")
        
        assert authenticated_user is None
    
    def test_create_access_token(self, db_session: Session):
        """Test access token creation"""
        auth_service = AuthService(db_session)
        
        # Create user
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Create token
        token = auth_service.create_access_token(user)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_token(self, db_session: Session):
        """Test token verification"""
        auth_service = AuthService(db_session)
        
        # Create user
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Create and verify token
        token = auth_service.create_access_token(user)
        token_data = auth_service.verify_token(token)
        
        assert token_data["username"] == "testuser"
        assert token_data["user_id"] == user.id
        assert "exp" in token_data
    
    def test_verify_invalid_token(self, db_session: Session):
        """Test verification of invalid token"""
        auth_service = AuthService(db_session)
        
        with pytest.raises(AuthenticationError, match="Invalid token"):
            auth_service.verify_token("invalid_token")
    
    def test_generate_api_key(self, db_session: Session):
        """Test API key generation"""
        auth_service = AuthService(db_session)
        
        # Create user
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        
        # Generate API key
        api_key = auth_service.generate_api_key(user)
        
        assert api_key is not None
        assert api_key.startswith("cti_")
        assert len(api_key) > 10
        
        # Refresh user from DB
        db_session.refresh(user)
        assert user.api_key == api_key
        assert user.api_key_created_at is not None
    
    def test_validate_api_key(self, db_session: Session):
        """Test API key validation"""
        auth_service = AuthService(db_session)
        
        # Create user and API key
        user_data = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123",
            confirm_password="TestPassword123"
        )
        user = auth_service.create_user(user_data)
        api_key = auth_service.generate_api_key(user)
        
        # Validate API key
        validated_user = auth_service.validate_api_key(api_key)
        
        assert validated_user is not None
        assert validated_user.id == user.id
        assert validated_user.username == "testuser"
    
    def test_validate_invalid_api_key(self, db_session: Session):
        """Test validation of invalid API key"""
        auth_service = AuthService(db_session)
        
        validated_user = auth_service.validate_api_key("invalid_api_key")
        
        assert validated_user is None
    
    def test_password_hashing(self, db_session: Session):
        """Test password hashing and verification"""
        auth_service = AuthService(db_session)
        
        password = "TestPassword123"
        hashed = auth_service.hash_password(password)
        
        assert hashed != password
        assert auth_service.verify_password(password, hashed) is True
        assert auth_service.verify_password("wrong_password", hashed) is False
    
    def test_user_scopes(self, db_session: Session):
        """Test user scope generation"""
        auth_service = AuthService(db_session)
        
        # Create admin user
        admin_data = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="AdminPassword123",
            confirm_password="AdminPassword123",
            role="admin"
        )
        admin_user = auth_service.create_user(admin_data)
        
        admin_scopes = auth_service.get_user_scopes(admin_user)
        
        assert "ioc:read" in admin_scopes
        assert "ioc:write" in admin_scopes
        assert "system:config:write" in admin_scopes
        
        # Create viewer user
        viewer_data = UserCreate(
            username="viewer",
            email="<EMAIL>",
            password="ViewerPassword123",
            confirm_password="ViewerPassword123",
            role="viewer"
        )
        viewer_user = auth_service.create_user(viewer_data)
        
        viewer_scopes = auth_service.get_user_scopes(viewer_user)
        
        assert "ioc:read" in viewer_scopes
        assert "ioc:write" not in viewer_scopes
        assert "system:config:write" not in viewer_scopes


class TestAuthAPI:
    """Test authentication API endpoints"""
    
    def test_register_user(self, client: TestClient):
        """Test user registration endpoint"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "confirm_password": "TestPassword123",
            "full_name": "Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "User registered successfully"
        assert data["user"]["username"] == "testuser"
        assert data["user"]["email"] == "<EMAIL>"
    
    def test_register_duplicate_user(self, client: TestClient):
        """Test registering duplicate user"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "confirm_password": "TestPassword123"
        }
        
        # Register first user
        client.post("/api/v1/auth/register", json=user_data)
        
        # Try to register duplicate
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]
    
    def test_login_success(self, client: TestClient):
        """Test successful login"""
        # Register user first
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "confirm_password": "TestPassword123"
        }
        client.post("/api/v1/auth/register", json=user_data)
        
        # Login
        login_data = {
            "username": "testuser",
            "password": "TestPassword123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
    
    def test_login_wrong_password(self, client: TestClient):
        """Test login with wrong password"""
        # Register user first
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123",
            "confirm_password": "TestPassword123"
        }
        client.post("/api/v1/auth/register", json=user_data)
        
        # Try to login with wrong password
        login_data = {
            "username": "testuser",
            "password": "WrongPassword"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict):
        """Test getting current user info"""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "username" in data
        assert "email" in data
        assert "role" in data
    
    def test_generate_api_key(self, client: TestClient, auth_headers: dict):
        """Test API key generation"""
        response = client.post("/api/v1/auth/api-key", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "api_key" in data
        assert data["api_key"].startswith("cti_")
        assert data["message"] == "API key generated successfully"
