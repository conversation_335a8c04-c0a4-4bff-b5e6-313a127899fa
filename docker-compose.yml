version: '3.8'

services:
  # CTI Dashboard Backend API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_URL=postgresql://${POSTGRES_USER:-cti_user}:${POSTGRES_PASSWORD:-cti_password}@db:5432/${POSTGRES_DB:-cti_dashboard}
      - REDIS_URL=redis://redis:6379/0
      # Load API keys from environment
      - CYFIRMA_API_KEY=${CYFIRMA_API_KEY:-}
      - SHODAN_API_KEY=${SHODAN_API_KEY:-}
      - VIRUSTOTAL_API_KEY=${VIRUSTOTAL_API_KEY:-}
      - CENSYS_API_ID=${CENSYS_API_ID:-}
      - CENSYS_API_SECRET=${CENSYS_API_SECRET:-}
      - ZOOMEYE_API_KEY=${ZOOMEYE_API_KEY:-}
      - ABUSEIPDB_API_KEY=${ABUSEIPDB_API_KEY:-}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - cti-network

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-cti_dashboard}
      - POSTGRES_USER=${POSTGRES_USER:-cti_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-cti_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - cti-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - cti-network

  # Frontend (Nginx serving static files)
  frontend:
    image: nginx:alpine
    ports:
      - "3000:80"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - cti-network

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - cti-network
    profiles:
      - monitoring

  # Optional: Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - cti-network
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  cti-network:
    driver: bridge
