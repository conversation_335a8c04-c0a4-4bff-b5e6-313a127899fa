"""
Security utilities for CTI Dashboard
"""

import secrets
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any
from passlib.context import Crypt<PERSON>ontext
from jose import JW<PERSON>rror, jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from ..config.settings import get_settings, get_security_settings


# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token handling
security = HTTPBearer()
settings = get_settings()
security_settings = get_security_settings()


class TokenData(BaseModel):
    """Token payload data"""
    username: Optional[str] = None
    scopes: list[str] = []
    exp: Optional[datetime] = None


class User(BaseModel):
    """User model for authentication"""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: bool = False
    scopes: list[str] = []


class UserInDB(User):
    """User model with hashed password"""
    hashed_password: str


# Password utilities
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def generate_secure_token(length: int = 32) -> str:
    """Generate a cryptographically secure random token"""
    return secrets.token_urlsafe(length)


# JWT token utilities
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.access_token_expire_minutes
        )
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.secret_key, 
        algorithm=settings.algorithm
    )
    return encoded_jwt


def verify_token(token: str) -> TokenData:
    """Verify and decode JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        scopes: list = payload.get("scopes", [])
        exp: datetime = datetime.fromtimestamp(payload.get("exp", 0))
        
        token_data = TokenData(username=username, scopes=scopes, exp=exp)
        
    except JWTError:
        raise credentials_exception
    
    return token_data


# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user"""
    token = credentials.credentials
    token_data = verify_token(token)
    
    # In production, fetch user from database
    # For now, return a mock user
    user = User(
        username=token_data.username,
        scopes=token_data.scopes
    )
    
    if user.disabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if current_user.disabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


# Permission checking
class RequireScopes:
    """Dependency to require specific scopes"""
    
    def __init__(self, *required_scopes: str):
        self.required_scopes = required_scopes
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        for scope in self.required_scopes:
            if scope not in current_user.scopes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Operation requires scope: {scope}"
                )
        return current_user


# API key management
class APIKeyManager:
    """Manage API keys for external services"""

    def __init__(self):
        self._keys: Dict[str, str] = {}
        self._encrypted_keys: Dict[str, str] = {}
        self._service_metadata: Dict[str, Dict[str, Any]] = {}
        self._default_services = self._get_default_services()

    def _get_default_services(self) -> Dict[str, Dict[str, Any]]:
        """Get default service configurations"""
        return {
            'cyfirma': {
                'label': 'Cyfirma Threat Intelligence',
                'category': 'threat_intelligence',
                'description': 'Cyfirma threat intelligence API for threat actor data',
                'requires_secret': False
            },
            'virustotal': {
                'label': 'VirusTotal',
                'category': 'threat_intelligence',
                'description': 'VirusTotal API for malware and URL analysis',
                'requires_secret': False
            },
            'shodan': {
                'label': 'Shodan',
                'category': 'search_engines',
                'description': 'Shodan search engine for internet-connected devices',
                'requires_secret': False
            },
            'censys': {
                'label': 'Censys',
                'category': 'search_engines',
                'description': 'Censys search engine for internet assets',
                'requires_secret': True
            },
            'zoomeye': {
                'label': 'ZoomEye',
                'category': 'search_engines',
                'description': 'ZoomEye search engine for network devices',
                'requires_secret': False
            },
            'abuseipdb': {
                'label': 'AbuseIPDB',
                'category': 'threat_intelligence',
                'description': 'AbuseIPDB for IP reputation checking',
                'requires_secret': False
            },
            'openai': {
                'label': 'OpenAI',
                'category': 'ai',
                'description': 'OpenAI API for AI-powered analysis',
                'requires_secret': False
            },
            'deepseek': {
                'label': 'DeepSeek',
                'category': 'ai',
                'description': 'DeepSeek API for AI-powered analysis',
                'requires_secret': False
            },
            'gemini': {
                'label': 'Google Gemini',
                'category': 'ai',
                'description': 'Google Gemini API for AI-powered analysis',
                'requires_secret': False
            }
        }

    def set_api_key(self, service: str, key: str, encrypt: bool = True) -> None:
        """Set API key for a service"""
        if encrypt:
            encrypted_key = self._encrypt_key(key)
            self._encrypted_keys[service] = encrypted_key
        else:
            self._keys[service] = key
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a service"""
        # Try encrypted keys first
        if service in self._encrypted_keys:
            return self._decrypt_key(self._encrypted_keys[service])
        
        # Fall back to plain keys
        return self._keys.get(service)
    
    def remove_api_key(self, service: str) -> bool:
        """Remove API key for a service"""
        removed = False
        if service in self._keys:
            del self._keys[service]
            removed = True
        if service in self._encrypted_keys:
            del self._encrypted_keys[service]
            removed = True
        return removed
    
    def list_configured_services(self) -> list[str]:
        """List services with configured API keys"""
        return list(set(self._keys.keys()) | set(self._encrypted_keys.keys()))

    def add_custom_service(self, name: str, label: str, description: Optional[str] = None,
                          category: str = "custom", requires_secret: bool = False) -> None:
        """Add a custom service configuration"""
        self._service_metadata[name] = {
            'label': label,
            'category': category,
            'description': description or f"Custom {label} service",
            'requires_secret': requires_secret,
            'is_custom': True
        }

    def remove_service(self, service: str) -> bool:
        """Remove a service and its API keys"""
        removed = False

        # Remove API keys
        if service in self._keys:
            del self._keys[service]
            removed = True
        if service in self._encrypted_keys:
            del self._encrypted_keys[service]
            removed = True

        # Remove custom service metadata (don't remove default services)
        if service in self._service_metadata and self._service_metadata[service].get('is_custom', False):
            del self._service_metadata[service]
            removed = True

        return removed

    def get_service_info(self, service: str) -> Optional[Dict[str, Any]]:
        """Get service information"""
        # Check custom services first
        if service in self._service_metadata:
            return self._service_metadata[service]

        # Check default services
        if service in self._default_services:
            return self._default_services[service]

        return None

    def list_all_services(self) -> Dict[str, Dict[str, Any]]:
        """List all available services (default + custom)"""
        all_services = self._default_services.copy()
        all_services.update(self._service_metadata)

        # Add configuration status
        for service_name, service_info in all_services.items():
            service_info['has_key'] = service_name in self._keys or service_name in self._encrypted_keys
            service_info['has_secret'] = False  # Will be enhanced later for services requiring secrets

        return all_services

    def get_service_categories(self) -> list[str]:
        """Get list of available service categories"""
        categories = set()
        all_services = self.list_all_services()
        for service_info in all_services.values():
            categories.add(service_info.get('category', 'custom'))
        return sorted(list(categories))
    
    def _encrypt_key(self, key: str) -> str:
        """Encrypt API key (simplified - use proper encryption in production)"""
        # This is a simplified example - use proper encryption like Fernet
        return hashlib.sha256(f"{settings.secret_key}{key}".encode()).hexdigest()
    
    def _decrypt_key(self, encrypted_key: str) -> str:
        """Decrypt API key (simplified - implement proper decryption)"""
        # This is a placeholder - implement proper decryption
        # For now, return the encrypted key as-is
        return encrypted_key


# Rate limiting
class RateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self):
        self._requests: Dict[str, list] = {}
    
    def is_allowed(self, identifier: str, limit: int, window_seconds: int = 60) -> bool:
        """Check if request is allowed under rate limit"""
        now = datetime.utcnow()
        window_start = now - timedelta(seconds=window_seconds)
        
        # Clean old requests
        if identifier in self._requests:
            self._requests[identifier] = [
                req_time for req_time in self._requests[identifier]
                if req_time > window_start
            ]
        else:
            self._requests[identifier] = []
        
        # Check if under limit
        if len(self._requests[identifier]) >= limit:
            return False
        
        # Record this request
        self._requests[identifier].append(now)
        return True


# Input validation and sanitization
def sanitize_input(data: str) -> str:
    """Sanitize user input"""
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        data = data.replace(char, '')
    
    return data.strip()


def validate_ioc(ioc_value: str) -> bool:
    """Validate IoC format"""
    import re
    
    # Basic validation patterns
    patterns = {
        'ip': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$',
        'domain': r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$',
        'hash_md5': r'^[a-fA-F0-9]{32}$',
        'hash_sha1': r'^[a-fA-F0-9]{40}$',
        'hash_sha256': r'^[a-fA-F0-9]{64}$',
        'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        'url': r'^https?://[^\s/$.?#].[^\s]*$'
    }
    
    for pattern in patterns.values():
        if re.match(pattern, ioc_value):
            return True
    
    return False


# Global instances
api_key_manager = APIKeyManager()
rate_limiter = RateLimiter()
