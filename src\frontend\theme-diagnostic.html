<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI Dashboard - Theme Diagnostic Tool</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/visual-enhancements.css" rel="stylesheet">
    <!-- Theme CSS -->
    <link id="theme-css" href="css/themes/light-professional.css" rel="stylesheet">
    
    <style>
        .diagnostic-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-card);
        }
        
        .color-swatch {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            display: inline-block;
            margin: 0.25rem;
            border: 1px solid var(--border-color);
        }
        
        .theme-info {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-pass { background: var(--success-color); }
        .status-fail { background: var(--danger-color); }
        .status-warning { background: var(--warning-color); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-palette"></i> CTI Dashboard Theme Diagnostic</h1>
                    <button class="btn btn-primary" id="theme-toggle">
                        <i class="bi bi-moon-fill" id="theme-icon"></i>
                        Toggle Theme
                    </button>
                </div>
            </div>
        </div>

        <!-- Theme Status -->
        <div class="diagnostic-section">
            <h3><i class="bi bi-info-circle"></i> Theme Status</h3>
            <div class="theme-info">
                <p><strong>Current Theme:</strong> <span id="current-theme">Loading...</span></p>
                <p><strong>Theme CSS File:</strong> <span id="theme-file">Loading...</span></p>
                <p><strong>Body Classes:</strong> <span id="body-classes">Loading...</span></p>
                <p><strong>Local Storage:</strong> <span id="local-storage">Loading...</span></p>
            </div>
        </div>

        <!-- Color Palette Test -->
        <div class="diagnostic-section">
            <h3><i class="bi bi-palette-fill"></i> Color Palette Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Primary Colors</h5>
                    <div class="color-swatch" style="background: var(--primary-color);" title="Primary"></div>
                    <div class="color-swatch" style="background: var(--primary-dark);" title="Primary Dark"></div>
                    <div class="color-swatch" style="background: var(--primary-light);" title="Primary Light"></div>
                </div>
                <div class="col-md-6">
                    <h5>Background Colors</h5>
                    <div class="color-swatch" style="background: var(--bg-primary);" title="BG Primary"></div>
                    <div class="color-swatch" style="background: var(--bg-secondary);" title="BG Secondary"></div>
                    <div class="color-swatch" style="background: var(--bg-card);" title="BG Card"></div>
                </div>
            </div>
        </div>

        <!-- Component Test -->
        <div class="diagnostic-section">
            <h3><i class="bi bi-grid-3x3-gap"></i> Component Theme Test</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>Test Card</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text">This card should adapt to theme changes.</p>
                            <button class="btn btn-primary btn-sm">Primary Button</button>
                            <button class="btn btn-success btn-sm">Success Button</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="threat-level low">Low Threat</div><br><br>
                    <div class="threat-level medium">Medium Threat</div><br><br>
                    <div class="threat-level high">High Threat</div><br><br>
                    <div class="threat-level critical">Critical Threat</div>
                </div>
                <div class="col-md-4">
                    <div class="progress-ring">
                        <svg>
                            <circle class="bg-circle" cx="60" cy="60" r="45"></circle>
                            <circle class="progress-circle" cx="60" cy="60" r="45" style="stroke-dasharray: 180 283;"></circle>
                        </svg>
                        <div class="progress-text">65%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accessibility Test -->
        <div class="diagnostic-section">
            <h3><i class="bi bi-universal-access"></i> Accessibility Test</h3>
            <div id="accessibility-results">
                <p><span class="status-indicator status-pass"></span>Text contrast ratio check</p>
                <p><span class="status-indicator status-pass"></span>Background contrast check</p>
                <p><span class="status-indicator status-pass"></span>Button accessibility check</p>
            </div>
        </div>

        <!-- Visual Enhancements Test -->
        <div class="diagnostic-section">
            <h3><i class="bi bi-graph-up"></i> Visual Enhancements Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <h6>Animated Counter</h6>
                    <div class="animated-counter" id="test-counter">0</div>
                </div>
                <div class="col-md-6">
                    <h6>Heatmap Test</h6>
                    <div class="heatmap-grid" style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 2px; width: 200px;">
                        <div class="heatmap-cell intensity-0"></div>
                        <div class="heatmap-cell intensity-1"></div>
                        <div class="heatmap-cell intensity-2"></div>
                        <div class="heatmap-cell intensity-3"></div>
                        <div class="heatmap-cell intensity-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme management for diagnostic
        const themes = {
            'light-professional': {
                name: 'Light Professional',
                file: 'css/themes/light-professional.css',
                icon: 'bi-moon-fill'
            },
            'dark-professional': {
                name: 'Dark Professional', 
                file: 'css/themes/dark-professional.css',
                icon: 'bi-sun-fill'
            }
        };
        
        let currentTheme = localStorage.getItem('cti-dashboard-theme') || 'light-professional';
        
        function updateDiagnosticInfo() {
            document.getElementById('current-theme').textContent = themes[currentTheme].name;
            document.getElementById('theme-file').textContent = themes[currentTheme].file;
            document.getElementById('body-classes').textContent = document.body.className || 'none';
            document.getElementById('local-storage').textContent = localStorage.getItem('cti-dashboard-theme') || 'none';
        }
        
        function toggleTheme() {
            const newTheme = currentTheme === 'light-professional' ? 'dark-professional' : 'light-professional';
            
            const themeLink = document.getElementById('theme-css');
            const theme = themes[newTheme];
            
            if (themeLink && theme) {
                themeLink.href = theme.file;
                
                // Update body classes
                document.body.className = document.body.className.replace(/theme-\w+-\w+/g, '');
                document.body.classList.add(`theme-${newTheme}`);
                
                // Update icon
                const themeIcon = document.getElementById('theme-icon');
                if (themeIcon) {
                    themeIcon.className = `bi ${theme.icon}`;
                }
                
                currentTheme = newTheme;
                localStorage.setItem('cti-dashboard-theme', currentTheme);
                
                updateDiagnosticInfo();
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Apply initial theme
            const themeLink = document.getElementById('theme-css');
            const theme = themes[currentTheme];
            themeLink.href = theme.file;
            document.body.classList.add(`theme-${currentTheme}`);
            
            // Update icon
            const themeIcon = document.getElementById('theme-icon');
            themeIcon.className = `bi ${theme.icon}`;
            
            // Setup event listener
            document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
            
            // Update diagnostic info
            updateDiagnosticInfo();
            
            // Animate counter
            let counter = 0;
            const counterElement = document.getElementById('test-counter');
            const interval = setInterval(() => {
                counter += 5;
                counterElement.textContent = counter;
                if (counter >= 100) {
                    clearInterval(interval);
                }
            }, 100);
        });
    </script>
</body>
</html>
