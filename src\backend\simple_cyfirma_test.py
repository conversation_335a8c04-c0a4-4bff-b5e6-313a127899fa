#!/usr/bin/env python3
"""
Simple test for Cyfirma API endpoint
"""

import requests
import json

def test_cyfirma_endpoint():
    """Test the Cyfirma API endpoint with a simple request"""
    
    api_key = "N3gxYJNBzm1OFxnwwMgAItdgLrNffYCU"
    base_url = "https://decyfir.cyfirma.com/core/api-ua"
    
    # Test the threat actor endpoint
    url = f"{base_url}/threatactor/stix/v2.1"
    params = {"key": api_key}
    
    print(f"🔍 Testing Cyfirma API...")
    print(f"📋 URL: {url}")
    print(f"📋 API Key: {api_key[:10]}...")
    
    try:
        print("📡 Making request...")
        response = requests.get(url, params=params, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                data = response.json()
                print(f"📝 Response type: {type(data)}")
                if isinstance(data, list):
                    print(f"📝 Number of items: {len(data)}")
                elif isinstance(data, dict):
                    print(f"📝 Keys: {list(data.keys())}")
            except json.JSONDecodeError:
                print("📝 Response is not JSON")
                print(f"📝 Response text (first 200 chars): {response.text[:200]}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📝 Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_cyfirma_endpoint()
