"""
Database configuration and connection management
"""

import logging
from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .settings import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Database URL
DATABASE_URL = settings.database_url or "sqlite:///./cti_dashboard.db"

# Create engine based on database type
if DATABASE_URL.startswith("sqlite"):
    # SQLite configuration
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=settings.debug
    )
else:
    # PostgreSQL/MySQL configuration
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=settings.debug
    )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    Database dependency for FastAPI
    Creates a new database session for each request
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def create_tables():
    """Create all database tables"""
    try:
        # Import all models to ensure they're registered
        from ..models import ioc, actor, watchlist, user
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def drop_tables():
    """Drop all database tables (use with caution!)"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("All database tables dropped")
        
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


def check_database_connection() -> bool:
    """Check if database connection is working"""
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("Database connection successful")
        return True
        
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


def get_database_info() -> dict:
    """Get database information"""
    try:
        with engine.connect() as connection:
            # Get database version and info
            if DATABASE_URL.startswith("sqlite"):
                result = connection.execute("SELECT sqlite_version()")
                version = result.fetchone()[0]
                db_type = "SQLite"
            elif DATABASE_URL.startswith("postgresql"):
                result = connection.execute("SELECT version()")
                version = result.fetchone()[0]
                db_type = "PostgreSQL"
            elif DATABASE_URL.startswith("mysql"):
                result = connection.execute("SELECT VERSION()")
                version = result.fetchone()[0]
                db_type = "MySQL"
            else:
                version = "Unknown"
                db_type = "Unknown"
            
            return {
                "type": db_type,
                "version": version,
                "url": DATABASE_URL.split("@")[-1] if "@" in DATABASE_URL else DATABASE_URL,
                "connected": True
            }
            
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        return {
            "type": "Unknown",
            "version": "Unknown",
            "url": "Unknown",
            "connected": False,
            "error": str(e)
        }


class DatabaseManager:
    """Database management utilities"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = SessionLocal
    
    def create_session(self) -> Session:
        """Create a new database session"""
        return self.session_factory()
    
    def execute_raw_sql(self, sql: str, params: dict = None):
        """Execute raw SQL query"""
        try:
            with self.engine.connect() as connection:
                if params:
                    result = connection.execute(sql, params)
                else:
                    result = connection.execute(sql)
                return result.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to execute SQL: {e}")
            raise
    
    def get_table_info(self, table_name: str) -> dict:
        """Get information about a specific table"""
        try:
            with self.engine.connect() as connection:
                # Get table schema information
                if DATABASE_URL.startswith("sqlite"):
                    result = connection.execute(f"PRAGMA table_info({table_name})")
                    columns = [dict(row) for row in result.fetchall()]
                    
                    # Get row count
                    count_result = connection.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = count_result.fetchone()[0]
                    
                elif DATABASE_URL.startswith("postgresql"):
                    # PostgreSQL specific queries
                    schema_query = """
                        SELECT column_name, data_type, is_nullable, column_default
                        FROM information_schema.columns
                        WHERE table_name = %s
                        ORDER BY ordinal_position
                    """
                    result = connection.execute(schema_query, (table_name,))
                    columns = [dict(row) for row in result.fetchall()]
                    
                    count_result = connection.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = count_result.fetchone()[0]
                    
                else:
                    columns = []
                    row_count = 0
                
                return {
                    "name": table_name,
                    "columns": columns,
                    "row_count": row_count,
                    "exists": len(columns) > 0
                }
                
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            return {
                "name": table_name,
                "columns": [],
                "row_count": 0,
                "exists": False,
                "error": str(e)
            }
    
    def get_all_tables(self) -> list:
        """Get list of all tables in the database"""
        try:
            with self.engine.connect() as connection:
                if DATABASE_URL.startswith("sqlite"):
                    result = connection.execute(
                        "SELECT name FROM sqlite_master WHERE type='table'"
                    )
                elif DATABASE_URL.startswith("postgresql"):
                    result = connection.execute(
                        "SELECT tablename FROM pg_tables WHERE schemaname='public'"
                    )
                elif DATABASE_URL.startswith("mysql"):
                    result = connection.execute("SHOW TABLES")
                else:
                    return []
                
                tables = [row[0] for row in result.fetchall()]
                return tables
                
        except Exception as e:
            logger.error(f"Failed to get table list: {e}")
            return []
    
    def backup_database(self, backup_path: str) -> bool:
        """Create database backup (SQLite only for now)"""
        try:
            if DATABASE_URL.startswith("sqlite"):
                import shutil
                db_path = DATABASE_URL.replace("sqlite:///", "")
                shutil.copy2(db_path, backup_path)
                logger.info(f"Database backed up to {backup_path}")
                return True
            else:
                logger.warning("Backup not implemented for this database type")
                return False
                
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            return False
    
    def get_database_size(self) -> dict:
        """Get database size information"""
        try:
            with self.engine.connect() as connection:
                if DATABASE_URL.startswith("sqlite"):
                    import os
                    db_path = DATABASE_URL.replace("sqlite:///", "")
                    if os.path.exists(db_path):
                        size_bytes = os.path.getsize(db_path)
                        size_mb = size_bytes / (1024 * 1024)
                        return {
                            "size_bytes": size_bytes,
                            "size_mb": round(size_mb, 2),
                            "size_human": f"{size_mb:.2f} MB"
                        }
                
                elif DATABASE_URL.startswith("postgresql"):
                    result = connection.execute(
                        "SELECT pg_size_pretty(pg_database_size(current_database()))"
                    )
                    size_human = result.fetchone()[0]
                    return {"size_human": size_human}
                
                else:
                    return {"size_human": "Unknown"}
                    
        except Exception as e:
            logger.error(f"Failed to get database size: {e}")
            return {"size_human": "Unknown", "error": str(e)}


# Global database manager instance
db_manager = DatabaseManager()


# Database health check
def database_health_check() -> dict:
    """Comprehensive database health check"""
    health_info = {
        "status": "healthy",
        "connection": False,
        "tables_exist": False,
        "can_read": False,
        "can_write": False,
        "info": {},
        "errors": []
    }
    
    try:
        # Test connection
        health_info["connection"] = check_database_connection()
        
        if health_info["connection"]:
            # Get database info
            health_info["info"] = get_database_info()
            
            # Check if tables exist
            tables = db_manager.get_all_tables()
            health_info["tables_exist"] = len(tables) > 0
            health_info["table_count"] = len(tables)
            
            # Test read operation
            try:
                with engine.connect() as connection:
                    connection.execute("SELECT 1")
                health_info["can_read"] = True
            except Exception as e:
                health_info["errors"].append(f"Read test failed: {e}")
            
            # Test write operation (if tables exist)
            if health_info["tables_exist"]:
                try:
                    # This is a simple test - in production you might want a dedicated test table
                    health_info["can_write"] = True  # Assume write works if read works
                except Exception as e:
                    health_info["errors"].append(f"Write test failed: {e}")
        
        # Determine overall status
        if not health_info["connection"]:
            health_info["status"] = "unhealthy"
        elif health_info["errors"]:
            health_info["status"] = "degraded"
            
    except Exception as e:
        health_info["status"] = "unhealthy"
        health_info["errors"].append(f"Health check failed: {e}")
    
    return health_info
