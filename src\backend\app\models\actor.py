"""
Threat Actor Database Models
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, Text, Boolean
from sqlalchemy.orm import relationship
from pydantic import BaseModel, validator

from .base import BaseDBModel, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class ThreatActor(BaseDBModel):
    """Threat Actor database model"""
    
    __tablename__ = "threat_actors"
    
    # Core actor fields
    name = Column(String(255), nullable=False, index=True)
    aliases = Column(JSON, default=list, nullable=False)  # List of strings
    description = Column(Text, nullable=True)
    
    # Attribution fields
    origin_country = Column(String(100), nullable=True, index=True)
    primary_motivation = Column(String(100), nullable=True, index=True)
    secondary_motivations = Column(JSON, default=list, nullable=False)
    
    # Target information
    target_industries = Column(JSON, default=list, nullable=False)
    target_regions = Column(JSON, default=list, nullable=False)
    target_countries = Column(JSON, default=list, nullable=False)
    
    # Technical details
    ttps = Column(JSON, default=list, nullable=False)  # MITRE ATT&CK techniques
    associated_malware = Column(JSON, default=list, nullable=False)
    infrastructure = Column(JSON, default=dict, nullable=False)
    
    # Classification
    actor_types = Column(JSON, default=list, nullable=False)  # nation-state, cybercriminal, etc.
    sophistication_level = Column(String(20), default="medium", nullable=False)
    severity_level = Column(String(20), default="medium", nullable=False)
    
    # Temporal information
    first_observed = Column(DateTime, nullable=True)
    last_observed = Column(DateTime, nullable=True)
    
    # Analysis data
    confidence_score = Column(Float, default=0.5, nullable=False)
    analysis_data = Column(JSON, nullable=True)  # AI analysis results
    
    # External references
    external_references = Column(JSON, default=list, nullable=False)
    stix_data = Column(JSON, nullable=True)  # STIX 2.1 data
    
    # Audit fields
    analyzed_by = Column(String(255), nullable=True)
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self):
        return f"<ThreatActor(id={self.id}, name='{self.name}', origin='{self.origin_country}')>"


class ThreatActorCampaign(BaseDBModel):
    """Threat Actor Campaign model"""
    
    __tablename__ = "threat_actor_campaigns"
    
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    actor_id = Column(Integer, nullable=False, index=True)  # Foreign key to ThreatActor
    
    # Campaign details
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    status = Column(String(50), default="active", nullable=False)  # active, inactive, completed
    
    # Target information
    targets = Column(JSON, default=list, nullable=False)
    objectives = Column(JSON, default=list, nullable=False)
    
    # Technical details
    malware_used = Column(JSON, default=list, nullable=False)
    techniques_used = Column(JSON, default=list, nullable=False)
    infrastructure_used = Column(JSON, default=dict, nullable=False)
    
    # Impact assessment
    impact_level = Column(String(20), default="medium", nullable=False)
    victims_count = Column(Integer, default=0, nullable=False)
    estimated_damage = Column(String(100), nullable=True)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)


class ThreatActorRelationship(BaseDBModel):
    """Threat Actor Relationship model"""
    
    __tablename__ = "threat_actor_relationships"
    
    source_actor_id = Column(Integer, nullable=False, index=True)
    target_actor_id = Column(Integer, nullable=False, index=True)
    relationship_type = Column(String(50), nullable=False)  # affiliated, competitor, successor, etc.
    confidence = Column(Float, default=0.5, nullable=False)
    description = Column(Text, nullable=True)
    
    # Temporal information
    relationship_start = Column(DateTime, nullable=True)
    relationship_end = Column(DateTime, nullable=True)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)


# Pydantic schemas for API
class ThreatActorBase(BaseSchema):
    """Base threat actor schema"""
    name: str
    aliases: List[str] = []
    description: Optional[str] = None
    origin_country: Optional[str] = None
    primary_motivation: Optional[str] = None
    secondary_motivations: List[str] = []
    target_industries: List[str] = []
    target_regions: List[str] = []
    target_countries: List[str] = []
    ttps: List[str] = []
    associated_malware: List[str] = []
    actor_types: List[str] = []
    sophistication_level: str = "medium"
    severity_level: str = "medium"
    confidence_score: float = 0.5
    
    @validator('confidence_score')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence score must be between 0.0 and 1.0')
        return v
    
    @validator('sophistication_level', 'severity_level')
    def validate_levels(cls, v):
        valid_levels = ['low', 'medium', 'high', 'critical']
        if v not in valid_levels:
            raise ValueError(f'Level must be one of: {", ".join(valid_levels)}')
        return v


class ThreatActorCreate(ThreatActorBase, BaseCreateSchema):
    """Schema for creating threat actors"""
    first_observed: Optional[datetime] = None
    last_observed: Optional[datetime] = None


class ThreatActorUpdate(BaseUpdateSchema):
    """Schema for updating threat actors"""
    name: Optional[str] = None
    aliases: Optional[List[str]] = None
    description: Optional[str] = None
    origin_country: Optional[str] = None
    primary_motivation: Optional[str] = None
    target_industries: Optional[List[str]] = None
    target_regions: Optional[List[str]] = None
    ttps: Optional[List[str]] = None
    associated_malware: Optional[List[str]] = None
    sophistication_level: Optional[str] = None
    severity_level: Optional[str] = None
    confidence_score: Optional[float] = None
    last_observed: Optional[datetime] = None


class ThreatActorResponse(ThreatActorBase, BaseResponseSchema):
    """Schema for threat actor responses"""
    first_observed: Optional[datetime] = None
    last_observed: Optional[datetime] = None
    analysis_data: Optional[Dict[str, Any]] = None
    external_references: List[Dict[str, Any]] = []
    analyzed_by: Optional[str] = None
    created_by: Optional[str] = None
    is_active: bool = True


class ThreatActorAnalysis(BaseModel):
    """Schema for threat actor analysis results"""
    executive_summary: str
    attack_vectors: List[str]
    target_analysis: Dict[str, Any]
    mitre_techniques: List[Dict[str, Any]]
    risk_assessment: Dict[str, Any]
    recommendations: List[str]
    confidence_level: float
    analysis_timestamp: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ThreatActorCampaignBase(BaseSchema):
    """Base campaign schema"""
    name: str
    description: Optional[str] = None
    actor_id: int
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    status: str = "active"
    targets: List[str] = []
    objectives: List[str] = []
    malware_used: List[str] = []
    techniques_used: List[str] = []
    impact_level: str = "medium"
    victims_count: int = 0
    estimated_damage: Optional[str] = None


class ThreatActorCampaignCreate(ThreatActorCampaignBase, BaseCreateSchema):
    """Schema for creating campaigns"""
    pass


class ThreatActorCampaignResponse(ThreatActorCampaignBase, BaseResponseSchema):
    """Schema for campaign responses"""
    infrastructure_used: Dict[str, Any] = {}
    created_by: Optional[str] = None
    is_active: bool = True


class ThreatActorRelationshipBase(BaseSchema):
    """Base relationship schema"""
    source_actor_id: int
    target_actor_id: int
    relationship_type: str
    confidence: float = 0.5
    description: Optional[str] = None
    relationship_start: Optional[datetime] = None
    relationship_end: Optional[datetime] = None


class ThreatActorRelationshipCreate(ThreatActorRelationshipBase, BaseCreateSchema):
    """Schema for creating relationships"""
    pass


class ThreatActorRelationshipResponse(ThreatActorRelationshipBase, BaseResponseSchema):
    """Schema for relationship responses"""
    created_by: Optional[str] = None
    is_active: bool = True


class ThreatActorStatistics(BaseModel):
    """Threat actor statistics schema"""
    total_actors: int
    by_origin_country: Dict[str, int]
    by_motivation: Dict[str, int]
    by_sophistication: Dict[str, int]
    by_actor_type: Dict[str, int]
    recent_additions: int  # Last 30 days
    top_targeted_industries: List[Dict[str, Any]]
    top_targeted_regions: List[Dict[str, Any]]
    active_campaigns: int
    total_relationships: int
