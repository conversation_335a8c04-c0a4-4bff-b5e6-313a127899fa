"""
Exception handlers for CTI Dashboard
"""

import logging
from typing import Union
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from pydantic import ValidationError

logger = logging.getLogger(__name__)


class CTIException(Exception):
    """Base exception for CTI Dashboard"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or "CTI_ERROR"
        self.details = details or {}
        super().__init__(self.message)


class ConfigurationError(CTIException):
    """Configuration related errors"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "CONFIG_ERROR", details)


class AuthenticationError(CTIException):
    """Authentication related errors"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "AUTH_ERROR", details)


class AuthorizationError(CTIException):
    """Authorization related errors"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "AUTHZ_ERROR", details)


class ValidationError(CTIException):
    """Data validation errors"""
    
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, "VALIDATION_ERROR", details)


class ExternalServiceError(CTIException):
    """External service integration errors"""
    
    def __init__(self, service: str, message: str, details: dict = None):
        self.service = service
        details = details or {}
        details["service"] = service
        super().__init__(f"{service}: {message}", "EXTERNAL_SERVICE_ERROR", details)


class RateLimitError(CTIException):
    """Rate limiting errors"""
    
    def __init__(self, message: str = "Rate limit exceeded", details: dict = None):
        super().__init__(message, "RATE_LIMIT_ERROR", details)


async def cti_exception_handler(request: Request, exc: CTIException) -> JSONResponse:
    """Handle CTI custom exceptions"""
    logger.error(f"CTI Exception: {exc.error_code} - {exc.message}")
    
    status_code = 500
    if exc.error_code == "AUTH_ERROR":
        status_code = 401
    elif exc.error_code == "AUTHZ_ERROR":
        status_code = 403
    elif exc.error_code == "VALIDATION_ERROR":
        status_code = 400
    elif exc.error_code == "RATE_LIMIT_ERROR":
        status_code = 429
    elif exc.error_code == "CONFIG_ERROR":
        status_code = 500
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": True,
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "path": str(request.url.path)
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions"""
    logger.warning(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "error_code": f"HTTP_{exc.status_code}",
            "message": exc.detail,
            "path": str(request.url.path)
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle request validation errors"""
    logger.warning(f"Validation Error: {exc.errors()}")
    
    # Format validation errors
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    return JSONResponse(
        status_code=422,
        content={
            "error": True,
            "error_code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": {"validation_errors": errors},
            "path": str(request.url.path)
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions"""
    logger.error(f"Unhandled Exception: {type(exc).__name__} - {str(exc)}", exc_info=True)
    
    # Don't expose internal errors in production
    from ..config.settings import get_settings
    settings = get_settings()
    
    if settings.environment == "production":
        message = "An internal error occurred"
        details = {}
    else:
        message = str(exc)
        details = {
            "exception_type": type(exc).__name__,
            "traceback": str(exc)
        }
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "error_code": "INTERNAL_ERROR",
            "message": message,
            "details": details,
            "path": str(request.url.path)
        }
    )


async def not_found_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """Handle 404 errors"""
    return JSONResponse(
        status_code=404,
        content={
            "error": True,
            "error_code": "NOT_FOUND",
            "message": "Endpoint not found",
            "path": str(request.url.path),
            "available_endpoints": {
                "health": "/health",
                "api": "/api/v1",
                "docs": "/docs"
            }
        }
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup all exception handlers for the application"""
    
    # Custom CTI exceptions
    app.add_exception_handler(CTIException, cti_exception_handler)
    
    # HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Validation exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 404 handler
    app.add_exception_handler(404, not_found_handler)
    
    # General exception handler (catch-all)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers setup completed")
