@echo off
echo 🛡️ CTI Dashboard Backend Server
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if server.py exists
if not exist "server.py" (
    echo ❌ server.py not found!
    echo Make sure you're running this from the project root
    pause
    exit /b 1
)

REM Check if backend directory exists
if not exist "src\backend" (
    echo ❌ Backend directory not found!
    echo Make sure the project structure is correct
    pause
    exit /b 1
)

echo 🔧 Checking dependencies...
REM Check if requirements.txt exists and install dependencies
if exist "src\backend\requirements.txt" (
    echo 📦 Installing Python dependencies...
    pip install -r src\backend\requirements.txt
    if errorlevel 1 (
        echo ⚠️  Warning: Some dependencies may not have installed correctly
        echo You can continue, but some features might not work
        pause
    )
) else (
    echo ⚠️  requirements.txt not found, skipping dependency installation
)

echo.
echo 🚀 Starting CTI Dashboard Backend Server...
echo 📚 API Documentation: http://localhost:8000/docs
echo 🔍 Health Check: http://localhost:8000/health
echo 🌐 CORS configured for: http://localhost:8080
echo.
echo ⚠️  Make sure to configure your API keys in environment variables
echo 🔧 Press Ctrl+C to stop the server
echo.

REM Start the backend server
python server.py

echo.
echo 👋 Backend server stopped
pause
