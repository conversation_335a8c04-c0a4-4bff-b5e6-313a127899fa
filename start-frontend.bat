@echo off
echo 🛡️ CTI Dashboard Frontend Server
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

REM Check if frontend directory exists
if not exist "src\frontend" (
    echo ❌ Frontend directory not found!
    echo Make sure you're running this from the project root
    pause
    exit /b 1
)

REM Check if main.js exists
if not exist "main.js" (
    echo ❌ main.js not found!
    echo Make sure the project structure is correct
    pause
    exit /b 1
)

echo 📁 Starting frontend development server...
echo 🌐 Server will be available at: http://localhost:8080
echo 📚 Main dashboard: http://localhost:8080/src/frontend/index.html
echo 🎨 Component demo: http://localhost:8080/src/frontend/demo.html
echo.
echo ⚠️  Make sure your backend is running on http://127.0.0.1:8000
echo 🔧 Press Ctrl+C to stop the server
echo.

REM Create a simple Python HTTP server script for the new structure
echo import http.server > temp_server.py
echo import socketserver >> temp_server.py
echo import webbrowser >> temp_server.py
echo import os >> temp_server.py
echo import sys >> temp_server.py
echo from pathlib import Path >> temp_server.py
echo. >> temp_server.py
echo PORT = 8080 >> temp_server.py
echo Handler = http.server.SimpleHTTPRequestHandler >> temp_server.py
echo. >> temp_server.py
echo try: >> temp_server.py
echo     with socketserver.TCPServer(("", PORT), Handler) as httpd: >> temp_server.py
echo         print(f"🌐 Server running at: http://localhost:{PORT}") >> temp_server.py
echo         print(f"📚 Main dashboard: http://localhost:{PORT}/src/frontend/index.html") >> temp_server.py
echo         print("🔧 Press Ctrl+C to stop") >> temp_server.py
echo         try: >> temp_server.py
echo             webbrowser.open(f"http://localhost:{PORT}/src/frontend/index.html") >> temp_server.py
echo         except: >> temp_server.py
echo             pass >> temp_server.py
echo         httpd.serve_forever() >> temp_server.py
echo except KeyboardInterrupt: >> temp_server.py
echo     print("\n👋 Server stopped") >> temp_server.py
echo except Exception as e: >> temp_server.py
echo     print(f"❌ Error: {e}") >> temp_server.py

REM Start the server
python temp_server.py

REM Clean up
del temp_server.py

pause
