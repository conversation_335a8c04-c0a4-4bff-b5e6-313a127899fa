"""
Authentication and Authorization Service
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Op<PERSON>, Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from jose import JWTError, jwt

from ..models.user import User, UserSession, UserActivity, UserLogin, UserCreate
from ..config.settings import get_settings, get_security_settings
from ..core.exceptions import AuthenticationError, AuthorizationError
from ..config.database import get_db

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Settings
settings = get_settings()
security_settings = get_security_settings()


class AuthService:
    """Authentication and authorization service"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_user(self, user_data: UserCreate, created_by: str = None) -> User:
        """Create a new user"""
        try:
            # Check if username already exists
            existing_user = self.db.query(User).filter(User.username == user_data.username).first()
            if existing_user:
                raise AuthenticationError("Username already exists")
            
            # Check if email already exists
            existing_email = self.db.query(User).filter(User.email == user_data.email).first()
            if existing_email:
                raise AuthenticationError("Email already exists")
            
            # Hash password
            hashed_password = self.hash_password(user_data.password)
            
            # Create user
            user = User(
                username=user_data.username,
                email=user_data.email,
                full_name=user_data.full_name,
                hashed_password=hashed_password,
                role=user_data.role,
                status=user_data.status,
                department=user_data.department,
                organization=user_data.organization,
                phone=user_data.phone,
                timezone=user_data.timezone,
                created_by=created_by,
                password_changed_at=datetime.utcnow()
            )
            
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"User created: {user.username} by {created_by}")
            return user
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create user: {e}")
            raise
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None, user_agent: str = None) -> Optional[User]:
        """Authenticate user with username and password"""
        try:
            # Get user
            user = self.db.query(User).filter(User.username == username).first()
            if not user:
                self.log_activity(None, "login_failed", metadata={"reason": "user_not_found", "username": username})
                return None
            
            # Check if user is locked
            if user.locked_until and user.locked_until > datetime.utcnow():
                self.log_activity(user.id, "login_failed", metadata={"reason": "account_locked"})
                raise AuthenticationError("Account is locked")
            
            # Check if user is active
            if not user.is_active or user.status != "active":
                self.log_activity(user.id, "login_failed", metadata={"reason": "account_inactive"})
                raise AuthenticationError("Account is inactive")
            
            # Verify password
            if not self.verify_password(password, user.hashed_password):
                # Increment failed attempts
                user.failed_login_attempts += 1
                
                # Lock account if too many failed attempts
                if user.failed_login_attempts >= security_settings.max_login_attempts:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=security_settings.lockout_duration_minutes)
                    logger.warning(f"Account locked for user {username} due to failed login attempts")
                
                self.db.commit()
                self.log_activity(user.id, "login_failed", metadata={"reason": "invalid_password"})
                return None
            
            # Reset failed attempts on successful login
            user.failed_login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            self.db.commit()
            
            self.log_activity(user.id, "login_success", ip_address=ip_address, user_agent=user_agent)
            logger.info(f"User authenticated: {username}")
            return user
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise AuthenticationError("Authentication failed")
    
    def create_access_token(self, user: User, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        try:
            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
            
            to_encode = {
                "sub": user.username,
                "user_id": user.id,
                "role": user.role,
                "scopes": self.get_user_scopes(user),
                "exp": expire
            }
            
            encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
            return encoded_jwt
            
        except Exception as e:
            logger.error(f"Failed to create access token: {e}")
            raise AuthenticationError("Failed to create access token")
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            username: str = payload.get("sub")
            user_id: int = payload.get("user_id")
            
            if username is None or user_id is None:
                raise AuthenticationError("Invalid token")
            
            # Check if user still exists and is active
            user = self.db.query(User).filter(User.id == user_id, User.username == username).first()
            if not user or not user.is_active:
                raise AuthenticationError("User not found or inactive")
            
            return {
                "username": username,
                "user_id": user_id,
                "role": payload.get("role"),
                "scopes": payload.get("scopes", []),
                "exp": payload.get("exp")
            }
            
        except JWTError as e:
            logger.warning(f"JWT verification failed: {e}")
            raise AuthenticationError("Invalid token")
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            raise AuthenticationError("Token verification failed")
    
    def create_session(self, user: User, ip_address: str = None, user_agent: str = None, device_info: Dict = None) -> UserSession:
        """Create user session"""
        try:
            session_id = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(minutes=security_settings.session_timeout_minutes)
            
            session = UserSession(
                session_id=session_id,
                user_id=user.id,
                ip_address=ip_address,
                user_agent=user_agent,
                device_info=device_info or {},
                expires_at=expires_at,
                last_activity=datetime.utcnow(),
                login_method="password"
            )
            
            self.db.add(session)
            self.db.commit()
            self.db.refresh(session)
            
            logger.info(f"Session created for user {user.username}")
            return session
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create session: {e}")
            raise
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """Validate user session"""
        try:
            session = self.db.query(UserSession).filter(
                UserSession.session_id == session_id,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.utcnow()
            ).first()
            
            if session:
                # Update last activity
                session.last_activity = datetime.utcnow()
                self.db.commit()
            
            return session
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return None
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalidate user session"""
        try:
            session = self.db.query(UserSession).filter(UserSession.session_id == session_id).first()
            if session:
                session.is_active = False
                self.db.commit()
                logger.info(f"Session invalidated: {session_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to invalidate session: {e}")
            return False
    
    def generate_api_key(self, user: User) -> str:
        """Generate API key for user"""
        try:
            api_key = f"cti_{secrets.token_urlsafe(32)}"
            user.api_key = api_key
            user.api_key_created_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"API key generated for user {user.username}")
            return api_key
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to generate API key: {e}")
            raise
    
    def validate_api_key(self, api_key: str) -> Optional[User]:
        """Validate API key"""
        try:
            user = self.db.query(User).filter(
                User.api_key == api_key,
                User.is_active == True,
                User.status == "active"
            ).first()
            
            if user:
                user.api_key_last_used = datetime.utcnow()
                self.db.commit()
                self.log_activity(user.id, "api_key_used")
            
            return user
            
        except Exception as e:
            logger.error(f"API key validation error: {e}")
            return None
    
    def get_user_scopes(self, user: User) -> list:
        """Get user scopes based on role and permissions"""
        role_scopes = {
            "admin": [
                "ioc:read", "ioc:write", "ioc:delete",
                "actor:read", "actor:analyze", "actor:report",
                "passive:scan", "passive:read",
                "watchlist:read", "watchlist:write", "watchlist:check",
                "system:read", "system:config:read", "system:config:write",
                "system:api-keys:read", "system:api-keys:write",
                "system:maintenance", "user:read", "user:write", "user:delete"
            ],
            "analyst": [
                "ioc:read", "ioc:write",
                "actor:read", "actor:analyze", "actor:report",
                "passive:scan", "passive:read",
                "watchlist:read", "watchlist:write", "watchlist:check",
                "system:read"
            ],
            "viewer": [
                "ioc:read", "actor:read", "passive:read",
                "watchlist:read", "system:read"
            ],
            "api_user": [
                "ioc:read", "ioc:write",
                "actor:read", "actor:analyze",
                "passive:scan", "passive:read",
                "watchlist:check"
            ]
        }
        
        scopes = role_scopes.get(user.role, [])
        
        # Add custom permissions
        if user.permissions:
            scopes.extend(user.permissions)
        
        return list(set(scopes))  # Remove duplicates
    
    def check_permission(self, user: User, required_scope: str) -> bool:
        """Check if user has required permission"""
        user_scopes = self.get_user_scopes(user)
        return required_scope in user_scopes
    
    def log_activity(self, user_id: int, action: str, resource: str = None, resource_id: str = None,
                    ip_address: str = None, user_agent: str = None, success: bool = True,
                    error_message: str = None, metadata: Dict = None):
        """Log user activity"""
        try:
            activity = UserActivity(
                user_id=user_id,
                action=action,
                resource=resource,
                resource_id=resource_id,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                error_message=error_message,
                metadata=metadata or {}
            )
            
            self.db.add(activity)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to log activity: {e}")
    
    def hash_password(self, password: str) -> str:
        """Hash password"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def change_password(self, user: User, current_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            # Verify current password
            if not self.verify_password(current_password, user.hashed_password):
                raise AuthenticationError("Current password is incorrect")
            
            # Hash new password
            user.hashed_password = self.hash_password(new_password)
            user.password_changed_at = datetime.utcnow()
            self.db.commit()
            
            self.log_activity(user.id, "password_changed")
            logger.info(f"Password changed for user {user.username}")
            return True
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to change password: {e}")
            raise


# Dependency injection
def get_auth_service(db: Session = None) -> AuthService:
    """Get authentication service instance"""
    if db is None:
        db = next(get_db())
    return AuthService(db)
