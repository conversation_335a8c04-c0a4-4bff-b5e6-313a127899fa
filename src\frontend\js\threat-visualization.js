/**
 * Threat Actor Visualization Module
 * Provides network graphs and relationship visualizations for threat intelligence data
 */

class ThreatVisualization {
    constructor() {
        this.charts = {};
        this.colors = {
            'nation-state': '#dc3545',
            'crime-syndicate': '#fd7e14', 
            'criminal': '#ffc107',
            'hacker': '#20c997',
            'terrorist': '#6f42c1',
            'unknown': '#6c757d'
        };
    }

    /**
     * Create a threat actor distribution chart
     */
    createActorTypeChart(containerId, data) {
        const ctx = document.getElementById(containerId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (this.charts[containerId]) {
            this.charts[containerId].destroy();
        }

        const chartData = {
            labels: Object.keys(data.actor_types),
            datasets: [{
                data: Object.values(data.actor_types),
                backgroundColor: Object.keys(data.actor_types).map(type => this.colors[type] || this.colors.unknown),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        this.charts[containerId] = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Create a geographic distribution chart
     */
    createGeographicChart(containerId, data) {
        const ctx = document.getElementById(containerId);
        if (!ctx) return;

        if (this.charts[containerId]) {
            this.charts[containerId].destroy();
        }

        // Get top 10 countries
        const countries = Object.entries(data.origin_countries)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);

        const chartData = {
            labels: countries.map(([country]) => country),
            datasets: [{
                label: 'Threat Actors',
                data: countries.map(([,count]) => count),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        this.charts[containerId] = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    /**
     * Create a severity distribution chart
     */
    createSeverityChart(containerId, data) {
        const ctx = document.getElementById(containerId);
        if (!ctx) return;

        if (this.charts[containerId]) {
            this.charts[containerId].destroy();
        }

        const severityColors = {
            'critical': '#dc3545',
            'high': '#fd7e14',
            'medium': '#ffc107',
            'low': '#28a745'
        };

        const chartData = {
            labels: Object.keys(data.severity_distribution),
            datasets: [{
                data: Object.values(data.severity_distribution),
                backgroundColor: Object.keys(data.severity_distribution).map(level => severityColors[level]),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        this.charts[containerId] = new Chart(ctx, {
            type: 'pie',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    /**
     * Create a target industries chart
     */
    createIndustriesChart(containerId, data) {
        const ctx = document.getElementById(containerId);
        if (!ctx) return;

        if (this.charts[containerId]) {
            this.charts[containerId].destroy();
        }

        // Get top 10 industries
        const industries = Object.entries(data.top_target_industries)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);

        const chartData = {
            labels: industries.map(([industry]) => industry.length > 15 ? industry.substring(0, 15) + '...' : industry),
            datasets: [{
                label: 'Targeted by # of Actors',
                data: industries.map(([,count]) => count),
                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        };

        this.charts[containerId] = new Chart(ctx, {
            type: 'horizontalBar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                // Show full industry name in tooltip
                                const index = context[0].dataIndex;
                                return industries[index][0];
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Create a simple network visualization for threat actor relationships
     */
    createNetworkVisualization(containerId, actors) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        // Create a simple force-directed layout using D3.js concepts
        // For now, we'll create a simplified version without D3 dependency
        
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '400');
        svg.style.border = '1px solid #dee2e6';
        svg.style.borderRadius = '0.375rem';

        // Create nodes for each actor
        const nodes = actors.slice(0, 20).map((actor, index) => ({
            id: actor.stix_id,
            name: actor.name,
            type: actor.actor_types[0] || 'unknown',
            severity: actor.severity_level,
            x: 100 + (index % 5) * 120,
            y: 100 + Math.floor(index / 5) * 80
        }));

        // Create simple connections based on shared target industries
        const connections = [];
        for (let i = 0; i < nodes.length; i++) {
            for (let j = i + 1; j < nodes.length; j++) {
                const actor1 = actors[i];
                const actor2 = actors[j];
                
                // Check for shared target industries
                const sharedIndustries = actor1.target_industries.filter(industry => 
                    actor2.target_industries.includes(industry)
                );
                
                if (sharedIndustries.length > 0) {
                    connections.push({
                        source: nodes[i],
                        target: nodes[j],
                        strength: sharedIndustries.length
                    });
                }
            }
        }

        // Draw connections
        connections.forEach(conn => {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', conn.source.x);
            line.setAttribute('y1', conn.source.y);
            line.setAttribute('x2', conn.target.x);
            line.setAttribute('y2', conn.target.y);
            line.setAttribute('stroke', '#6c757d');
            line.setAttribute('stroke-width', Math.min(conn.strength, 3));
            line.setAttribute('opacity', '0.6');
            svg.appendChild(line);
        });

        // Draw nodes
        nodes.forEach(node => {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', node.x);
            circle.setAttribute('cy', node.y);
            circle.setAttribute('r', '8');
            circle.setAttribute('fill', this.colors[node.type] || this.colors.unknown);
            circle.setAttribute('stroke', '#fff');
            circle.setAttribute('stroke-width', '2');
            circle.style.cursor = 'pointer';
            
            // Add tooltip
            circle.innerHTML = `<title>${node.name} (${node.type})</title>`;
            
            svg.appendChild(circle);

            // Add label
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', node.x);
            text.setAttribute('y', node.y + 25);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('font-size', '10');
            text.setAttribute('fill', '#495057');
            text.textContent = node.name.length > 12 ? node.name.substring(0, 12) + '...' : node.name;
            svg.appendChild(text);
        });

        container.appendChild(svg);

        // Add legend
        const legend = document.createElement('div');
        legend.className = 'mt-2 d-flex flex-wrap gap-2';
        legend.innerHTML = Object.entries(this.colors).map(([type, color]) => 
            `<span class="badge" style="background-color: ${color}">${type.replace('-', ' ')}</span>`
        ).join('');
        container.appendChild(legend);
    }

    /**
     * Destroy all charts
     */
    destroyAll() {
        Object.values(this.charts).forEach(chart => chart.destroy());
        this.charts = {};
    }
}

// Global instance
window.threatVisualization = new ThreatVisualization();
