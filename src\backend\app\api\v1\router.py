"""
Main API router for CTI Dashboard v1
"""

from fastapi import APIRouter

from .endpoints import auth, ioc, actors, passive, watchlist, system

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    ioc.router,
    prefix="/ioc",
    tags=["IoC Management"]
)

api_router.include_router(
    actors.router,
    prefix="/actors",
    tags=["Threat Actors"]
)

api_router.include_router(
    passive.router,
    prefix="/passive",
    tags=["Passive Scanning"]
)

api_router.include_router(
    watchlist.router,
    prefix="/watchlist",
    tags=["Watchlist Monitoring"]
)

api_router.include_router(
    system.router,
    prefix="/system",
    tags=["System Management"]
)
