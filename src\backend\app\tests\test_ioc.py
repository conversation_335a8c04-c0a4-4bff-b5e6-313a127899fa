"""
IoC (Indicators of Compromise) tests
"""

import pytest
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from ..models.ioc import IoC, IoCCreate
from ..services.ioc_service import IoCService


class TestIoCService:
    """Test IoC service"""
    
    def test_determine_ioc_type(self, db_session: Session):
        """Test IoC type determination"""
        ioc_service = IoCService(db_session, None)  # No integration manager for this test
        
        # Test IP address
        assert ioc_service._determine_ioc_type("***********") == "ip"
        assert ioc_service._determine_ioc_type("********") == "ip"
        
        # Test domain
        assert ioc_service._determine_ioc_type("example.com") == "domain"
        assert ioc_service._determine_ioc_type("malicious.example.org") == "domain"
        
        # Test hashes
        assert ioc_service._determine_ioc_type("d41d8cd98f00b204e9800998ecf8427e") == "md5"
        assert ioc_service._determine_ioc_type("da39a3ee5e6b4b0d3255bfef95601890afd80709") == "sha1"
        assert ioc_service._determine_ioc_type("e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855") == "sha256"
        
        # Test email
        assert ioc_service._determine_ioc_type("<EMAIL>") == "email"
        
        # Test URL
        assert ioc_service._determine_ioc_type("http://malicious.example.com") == "url"
        assert ioc_service._determine_ioc_type("https://evil.com/malware") == "url"
        
        # Test unknown
        assert ioc_service._determine_ioc_type("unknown_format") == "unknown"
    
    @pytest.mark.asyncio
    async def test_ingest_ioc(self, db_session: Session, mock_external_apis):
        """Test IoC ingestion"""
        ioc_service = IoCService(db_session, None)
        
        ioc = await ioc_service.ingest_ioc(
            value="***********",
            source="test_source",
            threat_actor="test_actor",
            malware_family="test_malware",
            tags=["test", "malicious"],
            created_by="test_user"
        )
        
        assert ioc.value == "***********"
        assert ioc.ioc_type == "ip"
        assert ioc.source == "test_source"
        assert ioc.threat_actor == "test_actor"
        assert ioc.malware_family == "test_malware"
        assert "test" in ioc.tags
        assert "malicious" in ioc.tags
        assert ioc.created_by == "test_user"
        assert ioc.confidence >= 0.0
        assert ioc.confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_ingest_duplicate_ioc(self, db_session: Session):
        """Test ingesting duplicate IoC updates existing one"""
        ioc_service = IoCService(db_session, None)
        
        # Ingest first IoC
        ioc1 = await ioc_service.ingest_ioc(
            value="***********",
            source="source1",
            threat_actor="actor1",
            created_by="user1"
        )
        
        # Ingest same IoC with different data
        ioc2 = await ioc_service.ingest_ioc(
            value="***********",
            source="source2",
            threat_actor="actor2",
            tags=["new_tag"],
            created_by="user2"
        )
        
        # Should be the same IoC, updated
        assert ioc1.id == ioc2.id
        assert ioc2.threat_actor == "actor2"  # Updated
        assert "new_tag" in ioc2.tags
        assert ioc2.last_seen > ioc1.last_seen
    
    @pytest.mark.asyncio
    async def test_batch_ingest(self, db_session: Session):
        """Test batch IoC ingestion"""
        ioc_service = IoCService(db_session, None)
        
        ioc_data_list = [
            {
                "value": "***********",
                "source": "test_source",
                "threat_actor": "actor1",
                "created_by": "test_user"
            },
            {
                "value": "malicious.com",
                "source": "test_source",
                "threat_actor": "actor2",
                "created_by": "test_user"
            },
            {
                "value": "d41d8cd98f00b204e9800998ecf8427e",
                "source": "test_source",
                "malware_family": "malware1",
                "created_by": "test_user"
            }
        ]
        
        iocs = await ioc_service.batch_ingest(ioc_data_list)
        
        assert len(iocs) == 3
        assert iocs[0].ioc_type == "ip"
        assert iocs[1].ioc_type == "domain"
        assert iocs[2].ioc_type == "md5"
    
    @pytest.mark.asyncio
    async def test_search_iocs(self, db_session: Session):
        """Test IoC search"""
        ioc_service = IoCService(db_session, None)
        
        # Create test IoCs
        await ioc_service.ingest_ioc("***********", "source1", threat_actor="actor1")
        await ioc_service.ingest_ioc("***********", "source1", threat_actor="actor2")
        await ioc_service.ingest_ioc("malicious.com", "source2", threat_actor="actor1")
        
        # Search by threat actor
        results = await ioc_service.search_iocs(threat_actor="actor1")
        assert results.total == 2
        
        # Search by IoC type
        results = await ioc_service.search_iocs(ioc_type="ip")
        assert results.total == 2
        
        # Search by query
        results = await ioc_service.search_iocs(query="192.168")
        assert results.total == 2
        
        # Search with limit
        results = await ioc_service.search_iocs(limit=1)
        assert len(results.items) == 1
    
    def test_calculate_confidence(self, db_session: Session):
        """Test confidence calculation"""
        ioc_service = IoCService(db_session, None)
        
        # Test with VirusTotal data
        enrichment_data = {
            "virustotal": {
                "last_analysis_stats": {
                    "malicious": 5,
                    "suspicious": 2,
                    "undetected": 60,
                    "harmless": 3
                }
            }
        }
        
        confidence = ioc_service._calculate_confidence(enrichment_data)
        expected = 5 / (5 + 2 + 60 + 3)  # malicious / total
        assert abs(confidence - expected) < 0.01
        
        # Test with AbuseIPDB data
        enrichment_data = {
            "abuseipdb": {
                "abuseConfidencePercentage": 75
            }
        }
        
        confidence = ioc_service._calculate_confidence(enrichment_data)
        assert abs(confidence - 0.75) < 0.01
        
        # Test with no enrichment data
        confidence = ioc_service._calculate_confidence({})
        assert confidence == 0.5


class TestIoCAPI:
    """Test IoC API endpoints"""
    
    def test_ingest_ioc(self, client: TestClient, auth_headers: dict):
        """Test IoC ingestion endpoint"""
        ioc_data = {
            "value": "***********",
            "source": "test_source",
            "threat_actor": "test_actor",
            "malware_family": "test_malware",
            "tags": ["test", "malicious"]
        }
        
        response = client.post("/api/v1/ioc/ingest", json=ioc_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["ioc"]["value"] == "***********"
        assert data["ioc"]["type"] == "ip"
        assert data["ioc"]["threat_actor"] == "test_actor"
    
    def test_ingest_invalid_ioc(self, client: TestClient, auth_headers: dict):
        """Test ingesting invalid IoC"""
        ioc_data = {
            "value": "",  # Empty value
            "source": "test_source"
        }
        
        response = client.post("/api/v1/ioc/ingest", json=ioc_data, headers=auth_headers)
        
        assert response.status_code == 400
    
    def test_batch_ingest_iocs(self, client: TestClient, auth_headers: dict):
        """Test batch IoC ingestion"""
        batch_data = {
            "iocs": [
                {
                    "value": "***********",
                    "source": "test_source",
                    "threat_actor": "actor1"
                },
                {
                    "value": "malicious.com",
                    "source": "test_source",
                    "threat_actor": "actor2"
                }
            ]
        }
        
        response = client.post("/api/v1/ioc/batch", json=batch_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["processed"] == 2
        assert data["total_requested"] == 2
    
    def test_batch_ingest_too_many_iocs(self, client: TestClient, auth_headers: dict):
        """Test batch ingestion with too many IoCs"""
        # Create more than 100 IoCs
        iocs = [{"value": f"192.168.1.{i}", "source": "test"} for i in range(101)]
        batch_data = {"iocs": iocs}
        
        response = client.post("/api/v1/ioc/batch", json=batch_data, headers=auth_headers)
        
        assert response.status_code == 400
        assert "cannot exceed 100" in response.json()["detail"]
    
    def test_search_iocs(self, client: TestClient, auth_headers: dict):
        """Test IoC search endpoint"""
        # First, ingest some IoCs
        ioc_data = {
            "value": "***********",
            "source": "test_source",
            "threat_actor": "test_actor"
        }
        client.post("/api/v1/ioc/ingest", json=ioc_data, headers=auth_headers)
        
        # Search for IoCs
        response = client.get("/api/v1/ioc/search?threat_actor=test_actor", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "iocs" in data
        assert "total" in data
    
    def test_get_ioc_by_id(self, client: TestClient, auth_headers: dict):
        """Test getting IoC by ID"""
        # First, ingest an IoC
        ioc_data = {
            "value": "***********",
            "source": "test_source"
        }
        response = client.post("/api/v1/ioc/ingest", json=ioc_data, headers=auth_headers)
        ioc_id = response.json()["ioc"]["id"]
        
        # Get IoC by ID
        response = client.get(f"/api/v1/ioc/{ioc_id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["ioc"]["id"] == ioc_id
        assert data["ioc"]["value"] == "***********"
    
    def test_get_nonexistent_ioc(self, client: TestClient, auth_headers: dict):
        """Test getting nonexistent IoC"""
        response = client.get("/api/v1/ioc/99999", headers=auth_headers)
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_delete_ioc(self, client: TestClient, auth_headers: dict):
        """Test deleting IoC"""
        # First, ingest an IoC
        ioc_data = {
            "value": "***********",
            "source": "test_source"
        }
        response = client.post("/api/v1/ioc/ingest", json=ioc_data, headers=auth_headers)
        ioc_id = response.json()["ioc"]["id"]
        
        # Delete IoC
        response = client.delete(f"/api/v1/ioc/{ioc_id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "deleted successfully" in data["message"]
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to IoC endpoints"""
        ioc_data = {
            "value": "***********",
            "source": "test_source"
        }
        
        response = client.post("/api/v1/ioc/ingest", json=ioc_data)
        
        assert response.status_code == 401
