# CTI Dashboard Frontend

A modern web interface for the Cyber Threat Intelligence Dashboard backend.

## Features

### 🛡️ Dashboard Overview
- Real-time system status monitoring
- Statistics cards showing active watchlist items, pending alerts, and system health
- Recent activity feed and latest alerts display

### 🔍 IoC Analysis
- Single IoC ingestion and enrichment
- Support for IPs, domains, hashes, and other indicators
- Real-time analysis results with confidence scoring
- Enrichment data from multiple threat intelligence sources

### 👤 Threat Actor Analysis
- Comprehensive threat actor profiling
- AI-powered analysis and risk assessment
- MITRE ATT&CK technique mapping
- Executive summary generation

### 🔎 Passive Scanning
- Multi-source passive reconnaissance
- Integration with Shodan, Censys, and ZoomEye
- Service discovery and vulnerability identification
- Port scanning and banner grabbing results

### 👁️ Watchlist Management
- Add indicators to monitoring watchlist
- Real-time alert generation
- Alert acknowledgment and management
- Flexible severity levels and tagging

### ⚙️ API Configuration
- Secure local storage of API keys
- Support for multiple AI services (Gemini, DeepSeek)
- Threat intelligence APIs (AbuseIPDB, VirusTotal)
- Search engine APIs (Shodan, Censys, ZoomEye)
- Connection testing and validation

## Setup Instructions

### Prerequisites
- A running CTI Dashboard backend (FastAPI server)
- Modern web browser with JavaScript enabled

### Installation
1. Ensure your backend is running on `http://127.0.0.1:8000`
2. Open `index.html` in your web browser, or serve it using a web server

### Using a Local Web Server (Recommended)
```bash
# Using Python's built-in server
cd frontend
python -m http.server 8080

# Using Node.js http-server
npx http-server -p 8080

# Using PHP's built-in server
php -S localhost:8080
```

Then open `http://localhost:8080` in your browser.

## Configuration

### API Keys Setup
1. Navigate to the **Settings** section
2. Enter your API keys for the services you want to use:
   - **Gemini API**: For AI-powered analysis
   - **DeepSeek API**: Alternative AI service
   - **AbuseIPDB**: IP reputation checking
   - **VirusTotal**: File and URL analysis
   - **Shodan**: Internet-connected device search
   - **Censys**: Internet scanning and analysis
   - **ZoomEye**: Cyberspace search engine

3. Click **Save Configuration** to store keys locally
4. Use **Test Connections** to verify your setup

### Security Notes
- API keys are stored in your browser's localStorage
- Keys are never transmitted to the backend unless explicitly configured
- Clear your browser data to remove stored keys
- Use the **Clear All Keys** button for quick cleanup

## Usage Guide

### Analyzing IoCs
1. Go to **IoC Analysis** section
2. Enter the indicator value (IP, domain, hash, etc.)
3. Specify the source and optional metadata
4. Click **Analyze IoC** to get enrichment data

### Threat Actor Research
1. Navigate to **Threat Actors** section
2. Fill in known information about the actor
3. Include TTPs, target industries, and associated malware
4. Click **Analyze Actor** for AI-powered insights

### Passive Reconnaissance
1. Open **Passive Scan** section
2. Enter target IP or domain
3. Select scan type (auto-detect recommended)
4. Choose which sources to query
5. Click **Start Passive Scan**

### Managing Watchlists
1. Go to **Watchlist** section
2. Add indicators you want to monitor
3. Set severity levels and descriptions
4. View and acknowledge alerts as they come in

## Browser Compatibility

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Troubleshooting

### Backend Connection Issues
- Verify the backend is running on `http://127.0.0.1:8000`
- Check the browser console for CORS errors
- Ensure the backend CORS settings allow your frontend domain

### API Key Issues
- Verify keys are correctly entered (no extra spaces)
- Check that services are accessible from your network
- Some APIs may have rate limits or require payment

### Performance Issues
- Large datasets may take time to load
- Consider using pagination for large watchlists
- Clear browser cache if experiencing issues

## Development

### File Structure
```
frontend/
├── index.html          # Main HTML file
├── css/
│   └── style.css      # Custom styles
├── js/
│   └── app.js         # Main application logic
└── README.md          # This file
```

### Customization
- Modify `css/style.css` for styling changes
- Update `js/app.js` for functionality changes
- The app uses Bootstrap 5 for responsive design

### API Integration
The frontend communicates with the backend using REST APIs:
- `GET /health` - Health check
- `POST /ioc/ingest` - IoC analysis
- `POST /actor/analyze` - Threat actor analysis
- `POST /passive/scan` - Passive scanning
- `GET/POST /watchlist/*` - Watchlist management

## Support

For issues or questions:
1. Check the browser console for error messages
2. Verify backend connectivity
3. Review API key configuration
4. Check network connectivity to external services

## License

This frontend is part of the CTI Dashboard project.
