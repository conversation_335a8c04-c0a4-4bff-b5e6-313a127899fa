"""
Pytest configuration and fixtures for CTI Dashboard tests
"""

import asyncio
import pytest
import pytest_asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from httpx import AsyncClient

from app.main import app
from app.config.settings import get_settings
from app.models.base import Base
from app.core.security import create_access_token


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session) -> Generator[TestClient, None, None]:
    """Create a test client"""
    
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    # Override database dependency
    # app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def test_user_token():
    """Create a test user token"""
    token_data = {
        "sub": "testuser",
        "scopes": ["read", "write", "admin"]
    }
    return create_access_token(token_data)


@pytest.fixture
def auth_headers(test_user_token):
    """Create authorization headers"""
    return {"Authorization": f"Bearer {test_user_token}"}


# Test data fixtures
@pytest.fixture
def sample_ioc_data():
    """Sample IoC data for testing"""
    return {
        "value": "***********",
        "source": "test_source",
        "threat_actor": "test_actor",
        "malware_family": "test_malware",
        "tags": ["test", "sample"]
    }


@pytest.fixture
def sample_threat_actor_data():
    """Sample threat actor data for testing"""
    return {
        "name": "Test Actor",
        "aliases": ["TestActor", "TA-001"],
        "description": "A test threat actor for unit testing",
        "origin_country": "Unknown",
        "motivation": ["financial"],
        "target_industries": ["technology"],
        "target_regions": ["global"],
        "ttps": ["T1566.001"],
        "associated_malware": ["test_malware"]
    }


@pytest.fixture
def sample_watchlist_item_data():
    """Sample watchlist item data for testing"""
    return {
        "value": "malicious.example.com",
        "item_type": "domain",
        "description": "Test malicious domain",
        "tags": ["test", "malware"],
        "severity": "high"
    }


@pytest.fixture
def mock_api_responses():
    """Mock API responses for external services"""
    return {
        "virustotal": {
            "data": {
                "attributes": {
                    "last_analysis_stats": {
                        "malicious": 5,
                        "suspicious": 2,
                        "undetected": 60,
                        "harmless": 3
                    }
                }
            }
        },
        "shodan": {
            "ip_str": "***********",
            "org": "Test Organization",
            "data": [
                {
                    "port": 80,
                    "banner": "HTTP/1.1 200 OK",
                    "product": "nginx"
                }
            ]
        },
        "cyfirma": {
            "objects": [
                {
                    "type": "threat-actor",
                    "name": "Test Actor",
                    "labels": ["cybercriminal"],
                    "primary_motivation": "financial-gain"
                }
            ]
        }
    }


# Mock external services
@pytest.fixture
def mock_external_apis(monkeypatch, mock_api_responses):
    """Mock external API calls"""
    
    async def mock_virustotal_request(*args, **kwargs):
        return mock_api_responses["virustotal"]
    
    async def mock_shodan_request(*args, **kwargs):
        return mock_api_responses["shodan"]
    
    async def mock_cyfirma_request(*args, **kwargs):
        return mock_api_responses["cyfirma"]
    
    # Patch the actual API call methods
    monkeypatch.setattr("app.services.integrations.virustotal.VirusTotalService.get_ip_report", mock_virustotal_request)
    monkeypatch.setattr("app.services.integrations.shodan.ShodanService.get_host_info", mock_shodan_request)
    monkeypatch.setattr("app.services.integrations.cyfirma.CyfirmaService.search_threat_actor", mock_cyfirma_request)


# Configuration fixtures
@pytest.fixture
def test_settings():
    """Test configuration settings"""
    settings = get_settings()
    settings.environment = "testing"
    settings.database_url = SQLALCHEMY_DATABASE_URL
    settings.secret_key = "test_secret_key_for_testing_only_32_chars"
    return settings


# Utility fixtures
@pytest.fixture
def cleanup_test_data():
    """Cleanup test data after tests"""
    yield
    # Cleanup code here if needed
    pass


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer for performance testing"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Database state fixtures
@pytest.fixture
def populated_db(db_session, sample_ioc_data, sample_threat_actor_data):
    """Database with sample data"""
    # Add sample data to database
    # This would create actual database records for integration tests
    yield db_session


# API testing utilities
class APITestHelper:
    """Helper class for API testing"""
    
    def __init__(self, client: TestClient):
        self.client = client
    
    def post_json(self, url: str, data: dict, headers: dict = None):
        """POST JSON data"""
        return self.client.post(url, json=data, headers=headers)
    
    def get_with_auth(self, url: str, headers: dict):
        """GET with authentication"""
        return self.client.get(url, headers=headers)
    
    def assert_success_response(self, response, expected_status=200):
        """Assert successful API response"""
        assert response.status_code == expected_status
        data = response.json()
        assert "success" in data
        assert data["success"] is True
        return data
    
    def assert_error_response(self, response, expected_status=400):
        """Assert error API response"""
        assert response.status_code == expected_status
        data = response.json()
        assert "detail" in data or "error" in data
        return data


@pytest.fixture
def api_helper(client):
    """API testing helper"""
    return APITestHelper(client)


# Async testing utilities
@pytest_asyncio.fixture
async def async_api_helper(async_client):
    """Async API testing helper"""
    
    class AsyncAPITestHelper:
        def __init__(self, client: AsyncClient):
            self.client = client
        
        async def post_json(self, url: str, data: dict, headers: dict = None):
            """POST JSON data asynchronously"""
            return await self.client.post(url, json=data, headers=headers)
        
        async def get_with_auth(self, url: str, headers: dict):
            """GET with authentication asynchronously"""
            return await self.client.get(url, headers=headers)
    
    return AsyncAPITestHelper(async_client)


# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.performance = pytest.mark.performance
pytest.mark.security = pytest.mark.security
