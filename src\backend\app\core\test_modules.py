#!/usr/bin/env python3
"""
Test script to verify CTI Dashboard modules work correctly
"""

import asyncio
import sys
from datetime import datetime

def test_imports():
    """Test that all modules can be imported"""
    print("Testing module imports...")

    try:
        from ioc_handler import IoC_Handler, IoC
        print("✓ IoC Handler imported successfully")
    except ImportError as e:
        print(f"✗ IoC Handler import failed: {e}")
        return False

    try:
        from actor_summary_agent import ActorSummaryAgent, ThreatActor
        print("✓ Actor Summary Agent imported successfully")
    except ImportError as e:
        print(f"✗ Actor Summary Agent import failed: {e}")
        return False

    try:
        from passive_scan import PassiveScanner
        print("✓ Passive Scanner imported successfully")
    except ImportError as e:
        print(f"✗ Passive Scanner import failed: {e}")
        return False

    try:
        from watchlist_monitor import WatchlistMonitor, WatchlistItem, WatchlistItemType, AlertSeverity
        print("✓ Watchlist Monitor imported successfully")
    except ImportError as e:
        print(f"✗ Watchlist Monitor import failed: {e}")
        return False

    return True

async def test_ioc_handler():
    """Test IoC Handler functionality"""
    print("\nTesting IoC Handler...")

    try:
        from ioc_handler import IoC_Handler, DEFAULT_CONFIG

        # Create handler with minimal config
        config = DEFAULT_CONFIG.copy()
        handler = IoC_Handler(config)

        # Test IoC type detection
        test_cases = [
            ("*******", "ip"),
            ("google.com", "domain"),
            ("http://example.com", "url"),
            ("d41d8cd98f00b204e9800998ecf8427e", "hash_md5"),
            ("<EMAIL>", "email")
        ]

        for value, expected_type in test_cases:
            detected_type = handler.detect_ioc_type(value)
            if detected_type == expected_type:
                print(f"✓ {value} -> {detected_type}")
            else:
                print(f"✗ {value} -> {detected_type} (expected {expected_type})")

        # Test IoC ingestion (without external APIs)
        ioc = await handler.ingest_ioc("test-domain.com", "test_source")
        print(f"✓ IoC ingested: {ioc.value} (type: {ioc.ioc_type}, confidence: {ioc.confidence})")

        return True

    except Exception as e:
        print(f"✗ IoC Handler test failed: {e}")
        return False

async def test_watchlist_monitor():
    """Test Watchlist Monitor functionality"""
    print("\nTesting Watchlist Monitor...")

    try:
        from watchlist_monitor import WatchlistMonitor, WatchlistItem, WatchlistItemType, AlertSeverity
        import uuid

        # Create monitor
        config = {'enable_subnet_matching': True}
        monitor = WatchlistMonitor(config)

        # Create test watchlist item
        item = WatchlistItem(
            id=str(uuid.uuid4()),
            value="test-domain.com",
            item_type=WatchlistItemType.DOMAIN,
            description="Test domain",
            added_date=datetime.utcnow(),
            added_by="test_user",
            tags=["test"],
            severity=AlertSeverity.MEDIUM
        )

        # Add to watchlist
        success = monitor.add_watchlist_item(item)
        print(f"✓ Watchlist item added: {success}")

        # Test intelligence check
        intel_data = {
            'domain': 'test-domain.com',
            'source': 'test_feed'
        }

        alerts = await monitor.check_intelligence_data(intel_data)
        print(f"✓ Intelligence check generated {len(alerts)} alerts")

        # Get statistics
        stats = monitor.get_watchlist_statistics()
        print(f"✓ Statistics: {stats['total_watchlist_items']} items, {stats['total_alerts']} alerts")

        return True

    except Exception as e:
        print(f"✗ Watchlist Monitor test failed: {e}")
        return False

def test_fastapi_app():
    """Test FastAPI app creation"""
    print("\nTesting FastAPI App...")

    try:
        # Test if we can create the app without running it
        import app
        print(f"✓ FastAPI app created: {app.app.title}")
        print(f"✓ Available routes: {len(app.app.routes)}")
        return True

    except Exception as e:
        print(f"✗ FastAPI App test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🛡️ CTI Dashboard Module Tests")
    print("=" * 40)

    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Cannot continue.")
        sys.exit(1)

    # Test individual modules
    tests = [
        test_ioc_handler(),
        test_watchlist_monitor()
    ]

    results = await asyncio.gather(*tests, return_exceptions=True)

    # Test FastAPI app (sync)
    app_result = test_fastapi_app()

    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")

    passed = sum(1 for r in results if r is True) + (1 if app_result else 0)
    total = len(results) + 1

    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("\n🚀 CTI Dashboard is ready to run!")
        print("Run: python app.py")
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print("Some modules may need attention.")

if __name__ == "__main__":
    asyncio.run(main())