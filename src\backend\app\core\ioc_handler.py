"""
IoC Handler Module - Ingestion and Enrichment of Indicators of Compromise
"""

import asyncio
import hashlib
import ipaddress
import re
from datetime import datetime
from typing import Dict, List, Optional, Union
from urllib.parse import urlparse

import aiohttp
from pydantic import BaseModel, validator


class IoC(BaseModel):
    """IoC data model"""
    value: str
    ioc_type: str
    source: str
    first_seen: datetime
    last_seen: Optional[datetime] = None
    confidence: float = 0.0
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    tags: List[str] = []
    enrichment_data: Dict = {}

    @validator('ioc_type')
    def validate_ioc_type(cls, v):
        valid_types = ['ip', 'domain', 'url', 'hash_md5', 'hash_sha1', 'hash_sha256', 'email']
        if v not in valid_types:
            raise ValueError(f'Invalid IoC type: {v}')
        return v


class IoC_Handler:
    """Main IoC handler class for ingestion and enrichment"""

    def __init__(self, config: Dict):
        self.config = config
        self.enrichment_sources = {
            'virustotal': self._enrich_virustotal,
            'abuseipdb': self._enrich_abuseipdb,
            'urlvoid': self._enrich_urlvoid,
            'hybrid_analysis': self._enrich_hybrid_analysis
        }

    def detect_ioc_type(self, value: str) -> str:
        """Automatically detect IoC type based on value"""
        value = value.strip().lower()

        # IP Address
        try:
            ipaddress.ip_address(value)
            return 'ip'
        except ValueError:
            pass

        # Hash detection
        if re.match(r'^[a-f0-9]{32}$', value):
            return 'hash_md5'
        elif re.match(r'^[a-f0-9]{40}$', value):
            return 'hash_sha1'
        elif re.match(r'^[a-f0-9]{64}$', value):
            return 'hash_sha256'

        # Email
        if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
            return 'email'

        # URL
        if value.startswith(('http://', 'https://', 'ftp://')):
            return 'url'

        # Domain (default fallback for domain-like strings)
        if re.match(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
            return 'domain'

        return 'unknown'

    async def ingest_ioc(self, value: str, source: str, **kwargs) -> IoC:
        """Ingest a new IoC with automatic type detection"""
        ioc_type = self.detect_ioc_type(value)

        ioc = IoC(
            value=value,
            ioc_type=ioc_type,
            source=source,
            first_seen=datetime.utcnow(),
            **kwargs
        )

        # Perform enrichment
        enriched_ioc = await self.enrich_ioc(ioc)

        return enriched_ioc

    async def enrich_ioc(self, ioc: IoC) -> IoC:
        """Enrich IoC with data from multiple sources"""
        enrichment_tasks = []

        for source_name, enrichment_func in self.enrichment_sources.items():
            if source_name in self.config.get('enabled_sources', []):
                enrichment_tasks.append(enrichment_func(ioc))

        # Run enrichment tasks concurrently
        enrichment_results = await asyncio.gather(*enrichment_tasks, return_exceptions=True)

        # Merge enrichment data
        for result in enrichment_results:
            if isinstance(result, dict):
                ioc.enrichment_data.update(result)

        # Calculate confidence score based on enrichment
        ioc.confidence = self._calculate_confidence(ioc)

        return ioc

    def _calculate_confidence(self, ioc: IoC) -> float:
        """Calculate confidence score based on enrichment data"""
        base_confidence = 0.5

        # Increase confidence based on number of sources
        source_count = len(ioc.enrichment_data)
        confidence_boost = min(source_count * 0.1, 0.4)

        # Adjust based on threat intelligence data
        if ioc.enrichment_data.get('malicious_count', 0) > 0:
            confidence_boost += 0.3

        if ioc.threat_actor:
            confidence_boost += 0.2

        return min(base_confidence + confidence_boost, 1.0)

    async def _enrich_virustotal(self, ioc: IoC) -> Dict:
        """Enrich IoC using VirusTotal API"""
        if not self.config.get('virustotal_api_key'):
            return {}

        try:
            headers = {'x-apikey': self.config['virustotal_api_key']}

            if ioc.ioc_type == 'ip':
                url = f"https://www.virustotal.com/vtapi/v2/ip-address/report"
                params = {'apikey': self.config['virustotal_api_key'], 'ip': ioc.value}
            elif ioc.ioc_type in ['hash_md5', 'hash_sha1', 'hash_sha256']:
                url = f"https://www.virustotal.com/vtapi/v2/file/report"
                params = {'apikey': self.config['virustotal_api_key'], 'resource': ioc.value}
            elif ioc.ioc_type == 'domain':
                url = f"https://www.virustotal.com/vtapi/v2/domain/report"
                params = {'apikey': self.config['virustotal_api_key'], 'domain': ioc.value}
            else:
                return {}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {'virustotal': data}

        except Exception as e:
            print(f"VirusTotal enrichment error: {e}")

        return {}

    async def _enrich_abuseipdb(self, ioc: IoC) -> Dict:
        """Enrich IP IoCs using AbuseIPDB"""
        if ioc.ioc_type != 'ip' or not self.config.get('abuseipdb_api_key'):
            return {}

        try:
            headers = {
                'Key': self.config['abuseipdb_api_key'],
                'Accept': 'application/json'
            }

            params = {
                'ipAddress': ioc.value,
                'maxAgeInDays': 90,
                'verbose': ''
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://api.abuseipdb.com/api/v2/check',
                    headers=headers,
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {'abuseipdb': data}

        except Exception as e:
            print(f"AbuseIPDB enrichment error: {e}")

        return {}

    async def _enrich_urlvoid(self, ioc: IoC) -> Dict:
        """Enrich domain/URL IoCs using URLVoid"""
        if ioc.ioc_type not in ['domain', 'url'] or not self.config.get('urlvoid_api_key'):
            return {}

        try:
            domain = ioc.value
            if ioc.ioc_type == 'url':
                domain = urlparse(ioc.value).netloc

            params = {
                'key': self.config['urlvoid_api_key'],
                'host': domain
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'http://api.urlvoid.com/api1000/host/scan/',
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.text()
                        return {'urlvoid': data}

        except Exception as e:
            print(f"URLVoid enrichment error: {e}")

        return {}

    async def _enrich_hybrid_analysis(self, ioc: IoC) -> Dict:
        """Enrich hash IoCs using Hybrid Analysis"""
        if ioc.ioc_type not in ['hash_md5', 'hash_sha1', 'hash_sha256'] or not self.config.get('hybrid_analysis_api_key'):
            return {}

        try:
            headers = {
                'api-key': self.config['hybrid_analysis_api_key'],
                'user-agent': 'CTI-Dashboard'
            }

            params = {'hash': ioc.value}

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://www.hybrid-analysis.com/api/v2/search/hash',
                    headers=headers,
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {'hybrid_analysis': data}

        except Exception as e:
            print(f"Hybrid Analysis enrichment error: {e}")

        return {}

    async def batch_ingest(self, ioc_list: List[Dict]) -> List[IoC]:
        """Batch ingest multiple IoCs"""
        tasks = []
        for ioc_data in ioc_list:
            task = self.ingest_ioc(**ioc_data)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and return successful results
        successful_iocs = [result for result in results if isinstance(result, IoC)]

        return successful_iocs


# Example usage and configuration
DEFAULT_CONFIG = {
    'enabled_sources': ['virustotal', 'abuseipdb', 'urlvoid', 'hybrid_analysis'],
    'virustotal_api_key': '',
    'abuseipdb_api_key': '',
    'urlvoid_api_key': '',
    'hybrid_analysis_api_key': ''
}


async def main():
    """Example usage of IoC Handler"""
    config = DEFAULT_CONFIG.copy()
    handler = IoC_Handler(config)

    # Example IoC ingestion
    test_iocs = [
        {'value': '*******', 'source': 'manual_input'},
        {'value': 'malicious-domain.com', 'source': 'threat_feed'},
        {'value': 'a1b2c3d4e5f6789012345678901234567890abcd', 'source': 'sandbox_analysis'}
    ]

    enriched_iocs = await handler.batch_ingest(test_iocs)

    for ioc in enriched_iocs:
        print(f"IoC: {ioc.value} | Type: {ioc.ioc_type} | Confidence: {ioc.confidence}")


if __name__ == "__main__":
    asyncio.run(main())