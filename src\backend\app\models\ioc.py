"""
IoC (Indicators of Compromise) Database Models
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, Text, Boolean
from sqlalchemy.dialects.postgresql import ARRAY
from pydantic import BaseModel, validator

from .base import BaseDBModel, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class IoC(BaseDBModel):
    """IoC database model"""
    
    __tablename__ = "iocs"
    
    # Core IoC fields
    value = Column(String(2048), nullable=False, index=True)
    ioc_type = Column(String(50), nullable=False, index=True)
    confidence = Column(Float, default=0.5, nullable=False)
    
    # Attribution fields
    source = Column(String(255), nullable=False)
    threat_actor = Column(String(255), nullable=True, index=True)
    malware_family = Column(String(255), nullable=True, index=True)
    
    # Temporal fields
    first_seen = Column(DateTime, nullable=True)
    last_seen = Column(DateTime, nullable=True)
    
    # Metadata
    tags = Column(JSON, default=list, nullable=False)  # List of strings
    enrichment_data = Column(JSON, nullable=True)  # Enrichment from external sources
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Additional metadata
    notes = Column(Text, nullable=True)
    severity = Column(String(20), default="medium", nullable=False)  # low, medium, high, critical
    
    def __repr__(self):
        return f"<IoC(id={self.id}, value='{self.value}', type='{self.ioc_type}', confidence={self.confidence})>"


# Pydantic schemas for API
class IoCBase(BaseSchema):
    """Base IoC schema"""
    value: str
    ioc_type: str
    confidence: float = 0.5
    source: str
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    tags: List[str] = []
    severity: str = "medium"
    notes: Optional[str] = None
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence must be between 0.0 and 1.0')
        return v
    
    @validator('severity')
    def validate_severity(cls, v):
        if v not in ['low', 'medium', 'high', 'critical']:
            raise ValueError('Severity must be one of: low, medium, high, critical')
        return v
    
    @validator('ioc_type')
    def validate_ioc_type(cls, v):
        valid_types = ['ip', 'domain', 'url', 'email', 'md5', 'sha1', 'sha256', 'unknown']
        if v not in valid_types:
            raise ValueError(f'IoC type must be one of: {", ".join(valid_types)}')
        return v


class IoCCreate(IoCBase, BaseCreateSchema):
    """Schema for creating IoCs"""
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None


class IoCUpdate(BaseUpdateSchema):
    """Schema for updating IoCs"""
    value: Optional[str] = None
    confidence: Optional[float] = None
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    tags: Optional[List[str]] = None
    severity: Optional[str] = None
    notes: Optional[str] = None
    last_seen: Optional[datetime] = None
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if v is not None and not 0.0 <= v <= 1.0:
            raise ValueError('Confidence must be between 0.0 and 1.0')
        return v


class IoCResponse(IoCBase, BaseResponseSchema):
    """Schema for IoC responses"""
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    enrichment_data: Optional[Dict[str, Any]] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    is_active: bool = True


class IoCSearchResult(BaseModel):
    """Search result container"""
    items: List[IoCResponse]
    total: int
    page: int
    size: int
    pages: int


class IoCEnrichmentData(BaseModel):
    """Schema for IoC enrichment data"""
    virustotal: Optional[Dict[str, Any]] = None
    abuseipdb: Optional[Dict[str, Any]] = None
    shodan: Optional[Dict[str, Any]] = None
    urlvoid: Optional[Dict[str, Any]] = None
    hybrid_analysis: Optional[Dict[str, Any]] = None
    cyfirma: Optional[Dict[str, Any]] = None
    
    class Config:
        extra = "allow"  # Allow additional enrichment sources


class IoCStatistics(BaseModel):
    """IoC statistics schema"""
    total_iocs: int
    by_type: Dict[str, int]
    by_severity: Dict[str, int]
    by_confidence: Dict[str, int]
    recent_additions: int  # Last 24 hours
    top_threat_actors: List[Dict[str, Any]]
    top_malware_families: List[Dict[str, Any]]


class IoCBulkOperation(BaseModel):
    """Schema for bulk operations"""
    operation: str  # create, update, delete
    iocs: List[Dict[str, Any]]
    
    @validator('operation')
    def validate_operation(cls, v):
        if v not in ['create', 'update', 'delete']:
            raise ValueError('Operation must be one of: create, update, delete')
        return v


class IoCRelationship(BaseDBModel):
    """IoC relationship model for tracking connections between IoCs"""
    
    __tablename__ = "ioc_relationships"
    
    source_ioc_id = Column(Integer, nullable=False, index=True)
    target_ioc_id = Column(Integer, nullable=False, index=True)
    relationship_type = Column(String(50), nullable=False)  # related, parent, child, similar
    confidence = Column(Float, default=0.5, nullable=False)
    description = Column(Text, nullable=True)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)


class IoCTag(BaseDBModel):
    """IoC tag model for better tag management"""
    
    __tablename__ = "ioc_tags"
    
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # Hex color code
    category = Column(String(50), nullable=True)  # malware, actor, campaign, etc.
    
    # Usage statistics
    usage_count = Column(Integer, default=0, nullable=False)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)


# Pydantic schemas for relationships and tags
class IoCRelationshipBase(BaseSchema):
    """Base IoC relationship schema"""
    source_ioc_id: int
    target_ioc_id: int
    relationship_type: str
    confidence: float = 0.5
    description: Optional[str] = None


class IoCRelationshipCreate(IoCRelationshipBase, BaseCreateSchema):
    """Schema for creating IoC relationships"""
    pass


class IoCRelationshipResponse(IoCRelationshipBase, BaseResponseSchema):
    """Schema for IoC relationship responses"""
    created_by: Optional[str] = None
    is_active: bool = True


class IoCTagBase(BaseSchema):
    """Base IoC tag schema"""
    name: str
    description: Optional[str] = None
    color: Optional[str] = None
    category: Optional[str] = None


class IoCTagCreate(IoCTagBase, BaseCreateSchema):
    """Schema for creating IoC tags"""
    pass


class IoCTagResponse(IoCTagBase, BaseResponseSchema):
    """Schema for IoC tag responses"""
    usage_count: int = 0
    created_by: Optional[str] = None
    is_active: bool = True
