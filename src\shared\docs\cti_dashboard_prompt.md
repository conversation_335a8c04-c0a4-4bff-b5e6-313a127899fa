
# 🛡️ CTI Dashboard Engineering Prompt

## 🎯 Objective

Act as a senior cybersecurity automation engineer with expertise in threat intelligence workflows, full-stack development, and AI integration.

I want you to help me build a modular and scalable **Cyber Threat Intelligence (CTI) Dashboard**.

---

## 🔧 Core Components

The dashboard will include the following **4 core components**:

1. **Threat Actor IoC Repository**  
   Store and enrich IPs, domains, hashes with context like threat actor, malware family, source.

2. **Passive Scanning Section**  
   Collect passive intelligence from sources like ZoomEye, Shodan, Censys.

3. **Threat Actor Library**  
   Auto-generate and manage threat actor profiles with TTP mapping (MITRE ATT&CK).

4. **Watchlist Monitoring**  
   Track high-risk entities (IPs/domains/actors) and alert when new intel matches watchlisted items.

---

## 🧠 Deliverables

Please provide a complete, clean, and well-structured design plan for the CTI dashboard project with:

- 📁 Recommended file/folder structure
- 🧩 Backend logic and API endpoint design (preferably with FastAPI)
- 🗃️ Suggestions for database (e.g., PostgreSQL, Supabase, MongoDB for JSON IoCs)
- 🎨 Frontend stack recommendation (e.g., Streamlit, Next.js, Manus frontend plugin)
- 🤖 Integration plan with LLMs (DeepSeek, OpenAI, Ollama)
- 📝 Example prompt for each LLM-powered module:
  - Threat actor summarizer
  - Passive scan explainer
  - IoC enrichment
- ⚙️ CI/CD folder structure for deployment automation
- 🧬 Optional vector DB design for RAG-based retrieval (e.g., FAISS or Milvus)

Final output should be:
- In bullet list form where needed
- Include code snippets
- Designed for SOC-readiness, future AI extensibility and modular automation

---

## ⚡ Bonus Request

```text
Now, based on the above, please generate:

- ioc_handler.py: to ingest and enrich new IoCs
- actor_summary_agent.py: LLM integration to summarize actor data using MITRE ATT&CK framework
- passive_scan.py: passive lookup to Censys/Shodan APIs
- watchlist_monitor.py: scan new intel and crossmatch with watchlist
- app.py: main FastAPI router to serve all modules

Make sure all modules are modular, reusable, and follow clean code principles.
```

---
